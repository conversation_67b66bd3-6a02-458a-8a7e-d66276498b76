/**
 * Auto Save on Leave Hook
 *
 * Copyright (c) 2025 waycaan
 * Licensed under the MIT License
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 */

import { useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/router';

interface UseAutoSaveOnLeaveOptions {
    enabled?: boolean;
}

const useAutoSaveOnLeave = (options: UseAutoSaveOnLeaveOptions = {}) => {
    const { enabled = true } = options;
    const router = useRouter();
    const isAutoSavingRef = useRef(false);


    const shouldAutoSave = useCallback(() => {
        if (typeof window !== 'undefined' && (window as any).saveButtonStatus) {
            const status = (window as any).saveButtonStatus;
            return status === 'save';
        }
        return false;
    }, []);

    const performAutoSave = useCallback(async () => {
        if (typeof window !== 'undefined' && (window as any).saveButtonAutoSave) {
            try {
                await (window as any).saveButtonAutoSave();
                return true;
            } catch (error) {
                return false;
            }
        }
        return false;
    }, []);

    // 页面关闭/刷新处理 - 弹窗提示机制
    const handleBeforeUnload = useCallback((event: BeforeUnloadEvent) => {
        if (!enabled) return;

        if (shouldAutoSave()) {
            console.log('🔧 页面关闭/刷新，有未保存内容，显示弹窗');
            // 显示确认对话框
            const message = '您有未保存的更改。确定要离开吗？';
            event.returnValue = message;

            // 使用延迟检测用户选择
            setTimeout(() => {
                // 如果能执行到这里，说明用户选择了"取消"，自动保存
                console.log('🔧 用户选择取消，执行自动保存');
                performAutoSave();
            }, 100);

            return message;
        }
    }, [enabled, shouldAutoSave, performAutoSave]);

    // 笔记跳转处理 - 区分笔记ID变化
    const handleRouteChangeStart = useCallback(async (url: string) => {
        if (!enabled || isAutoSavingRef.current) return;

        const shouldSave = shouldAutoSave();
        console.log('🔧 路由变化检测:', { url, shouldSave });

        if (shouldSave) {
            // 检查是否是笔记ID变化（笔记跳转）
            const isNoteNavigation = url.match(/^\/[a-zA-Z0-9-]+(\?.*)?$/) || url === '/' || url.includes('?new');
            console.log('🔧 路由类型:', { isNoteNavigation, url });

            // 阻止默认路由跳转
            throw new Error('Auto-saving before route change');
        }
    }, [enabled, shouldAutoSave]);

    // 单独处理自动保存和跳转
    const handleAutoSaveAndNavigate = useCallback(async (url: string) => {
        if (isAutoSavingRef.current) return;

        const isNoteNavigation = url.match(/^\/[a-zA-Z0-9-]+(\?.*)?$/) || url === '/' || url.includes('?new');

        if (isNoteNavigation) {
            // 笔记跳转：直接自动保存
            console.log('🔧 笔记跳转，开始自动保存');
            isAutoSavingRef.current = true;

            try {
                const success = await performAutoSave();
                console.log('🔧 自动保存结果:', success);

                if (success) {
                    console.log('🔧 自动保存成功，继续跳转');
                    isAutoSavingRef.current = false;
                    router.push(url);
                } else {
                    isAutoSavingRef.current = false;
                    const confirmed = window.confirm('自动保存失败。是否强制离开？');
                    if (confirmed) {
                        router.push(url);
                    }
                }
            } catch (error) {
                console.error('自动保存出错:', error);
                isAutoSavingRef.current = false;
                const confirmed = window.confirm('自动保存出错。是否强制离开？');
                if (confirmed) {
                    router.push(url);
                }
            }
        } else {
            // 非笔记跳转：弹窗提示
            const confirmed = window.confirm('您有未保存的更改。确定要离开吗？');
            if (confirmed) {
                router.push(url);
            } else {
                await performAutoSave();
            }
        }
    }, [performAutoSave, router]);



    useEffect(() => {
        if (!enabled) return;

        window.addEventListener('beforeunload', handleBeforeUnload);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [enabled, handleBeforeUnload]);

    useEffect(() => {
        if (!enabled) return;

        router.events.on('routeChangeStart', handleRouteChangeStart);

        // 设置全局函数供外部调用
        if (typeof window !== 'undefined') {
            (window as any).handleAutoSaveAndNavigate = handleAutoSaveAndNavigate;
        }

        return () => {
            router.events.off('routeChangeStart', handleRouteChangeStart);
            if (typeof window !== 'undefined') {
                delete (window as any).handleAutoSaveAndNavigate;
            }
        };
    }, [enabled, handleRouteChangeStart, handleAutoSaveAndNavigate, router.events]);

    return {
        shouldAutoSave,
        performAutoSave,
        handleAutoSaveAndNavigate,
    };
};

export default useAutoSaveOnLeave;
