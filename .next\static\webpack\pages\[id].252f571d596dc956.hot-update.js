"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/hooks/use-auto-save-on-leave.ts":
/*!**************************************************!*\
  !*** ./libs/web/hooks/use-auto-save-on-leave.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Auto Save on Leave Hook\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\n\n\nvar useAutoSaveOnLeave = function() {\n    var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _enabled = options.enabled, enabled = _enabled === void 0 ? true : _enabled;\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var isAutoSavingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    var shouldAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if ( true && window.saveButtonStatus) {\n            return window.saveButtonStatus === \"save\";\n        }\n        return false;\n    }, []);\n    var performAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function() {\n        var error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!( true && window.saveButtonAutoSave)) return [\n                        3,\n                        4\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        window.saveButtonAutoSave()\n                    ];\n                case 2:\n                    _state.sent();\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), []);\n    // 页面关闭/刷新处理 - 弹窗提示机制\n    var handleBeforeUnload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        if (!enabled) return;\n        if (shouldAutoSave()) {\n            // 阻止默认行为，显示确认对话框\n            event.preventDefault();\n            event.returnValue = \"您有未保存的更改。确定要离开吗？\";\n            // 使用延迟检测用户选择\n            setTimeout(function() {\n                // 如果能执行到这里，说明用户选择了\"取消\"，自动保存\n                performAutoSave();\n            }, 100);\n            return \"您有未保存的更改。确定要离开吗？\";\n        }\n    }, [\n        enabled,\n        shouldAutoSave,\n        performAutoSave\n    ]);\n    // 🔧 重构：笔记跳转处理 - 区分笔记ID变化\n    var handleRouteChangeStart = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function(url) {\n            var isNoteNavigation, success, confirmed, error, confirmed1, confirmed2;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!enabled || isAutoSavingRef.current) return [\n                            2\n                        ];\n                        if (!shouldAutoSave()) return [\n                            3,\n                            8\n                        ];\n                        console.log(\"\\uD83D\\uDD27 检测到路由跳转，有未保存内容\", {\n                            targetUrl: url\n                        });\n                        isNoteNavigation = url.match(/^\\/[a-zA-Z0-9-]+$/) || url === \"/\";\n                        if (!isNoteNavigation) return [\n                            3,\n                            5\n                        ];\n                        // 笔记跳转：直接自动保存\n                        console.log(\"\\uD83D\\uDD27 笔记跳转，执行自动保存\");\n                        isAutoSavingRef.current = true;\n                        // 阻止路由跳转\n                        router.events.emit(\"routeChangeError\", new Error(\"Auto-saving before route change\"), url);\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            performAutoSave()\n                        ];\n                    case 2:\n                        success = _state.sent();\n                        isAutoSavingRef.current = false;\n                        if (success) {\n                            console.log(\"\\uD83D\\uDD27 自动保存成功，继续跳转\");\n                            router.push(url);\n                        } else {\n                            console.log(\"\\uD83D\\uDD27 自动保存失败，询问用户\");\n                            confirmed = window.confirm(\"自动保存失败。是否强制离开？\");\n                            if (confirmed) {\n                                router.push(url);\n                            }\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"\\uD83D\\uDD27 自动保存出错:\", error);\n                        isAutoSavingRef.current = false;\n                        confirmed1 = window.confirm(\"自动保存出错。是否强制离开？\");\n                        if (confirmed1) {\n                            router.push(url);\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            3,\n                            8\n                        ];\n                    case 5:\n                        // 非笔记跳转：弹窗提示\n                        console.log(\"\\uD83D\\uDD27 非笔记跳转，显示弹窗提示\");\n                        router.events.emit(\"routeChangeError\", new Error(\"User confirmation required\"), url);\n                        confirmed2 = window.confirm(\"您有未保存的更改。确定要离开吗？\");\n                        if (!confirmed2) return [\n                            3,\n                            6\n                        ];\n                        console.log(\"\\uD83D\\uDD27 用户确认强制离开\");\n                        router.push(url);\n                        return [\n                            3,\n                            8\n                        ];\n                    case 6:\n                        console.log(\"\\uD83D\\uDD27 用户取消离开，执行自动保存\");\n                        return [\n                            4,\n                            performAutoSave()\n                        ];\n                    case 7:\n                        _state.sent();\n                        _state.label = 8;\n                    case 8:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(url) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        enabled,\n        shouldAutoSave,\n        performAutoSave,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return function() {\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        enabled,\n        handleBeforeUnload\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        router.events.on(\"routeChangeStart\", handleRouteChangeStart);\n        return function() {\n            router.events.off(\"routeChangeStart\", handleRouteChangeStart);\n        };\n    }, [\n        enabled,\n        handleRouteChangeStart,\n        router.events\n    ]);\n    return {\n        shouldAutoSave: shouldAutoSave,\n        performAutoSave: performAutoSave\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useAutoSaveOnLeave);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9ob29rcy91c2UtYXV0by1zYXZlLW9uLWxlYXZlLnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTs7Ozs7Ozs7Ozs7Ozs7O0NBZUMsR0FFRDs7QUFBdUQ7QUFDZjtBQU14QyxJQUFNSSxrQkFBa0IsR0FBRyxXQUE2QztRQUE1Q0MsT0FBa0Msb0VBQUcsRUFBRTtJQUMvRCxlQUEyQkEsT0FBTyxDQUExQkMsT0FBTyxFQUFQQSxPQUFPLHlCQUFHLElBQUk7SUFDdEIsSUFBTUMsTUFBTSxHQUFHSixzREFBUyxFQUFFO0lBQzFCLElBQU1LLGVBQWUsR0FBR04sNkNBQU0sQ0FBQyxLQUFLLENBQUM7SUFHckMsSUFBTU8sY0FBYyxHQUFHUixrREFBVyxDQUFDLFdBQU07UUFDckMsSUFBSSxLQUE2QixJQUFJLE1BQU8sQ0FBU1UsZ0JBQWdCLEVBQUU7WUFDbkUsT0FBTyxNQUFPLENBQVNBLGdCQUFnQixLQUFLLE1BQU0sQ0FBQztRQUN2RCxDQUFDO1FBQ0QsT0FBTyxLQUFLLENBQUM7SUFDakIsQ0FBQyxFQUFFLEVBQUUsQ0FBQztJQUVOLElBQU1DLGVBQWUsR0FBR1gsa0RBQVcsZUFBQywrRkFBWTtZQUsvQlksS0FBSzs7Ozt5QkFKZCxNQUE2QixJQUFJLE1BQU8sQ0FBU0Msa0JBQWtCLEdBQW5FOzs7c0JBQW1FOzs7Ozs7Ozs7b0JBRS9EOzt3QkFBTSxNQUFPLENBQVNBLGtCQUFrQixFQUFFO3NCQUFBOztvQkFBMUMsYUFBMEMsQ0FBQztvQkFDM0M7O3dCQUFPLElBQUk7c0JBQUM7O29CQUNQRCxLQUFLO29CQUNWOzt3QkFBTyxLQUFLO3NCQUFDOztvQkFHckI7O3dCQUFPLEtBQUs7c0JBQUM7OztJQUNqQixDQUFDLEdBQUUsRUFBRSxDQUFDO0lBRU4scUJBQXFCO0lBQ3JCLElBQU1FLGtCQUFrQixHQUFHZCxrREFBVyxDQUFDLFNBQUNlLEtBQXdCLEVBQUs7UUFDakUsSUFBSSxDQUFDVixPQUFPLEVBQUUsT0FBTztRQUVyQixJQUFJRyxjQUFjLEVBQUUsRUFBRTtZQUNsQixpQkFBaUI7WUFDakJPLEtBQUssQ0FBQ0MsY0FBYyxFQUFFLENBQUM7WUFDdkJELEtBQUssQ0FBQ0UsV0FBVyxHQUFHLGtCQUFrQixDQUFDO1lBRXZDLGFBQWE7WUFDYkMsVUFBVSxDQUFDLFdBQU07Z0JBQ2IsNEJBQTRCO2dCQUM1QlAsZUFBZSxFQUFFLENBQUM7WUFDdEIsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxDQUFDO1lBRVIsT0FBTyxrQkFBa0IsQ0FBQztRQUM5QixDQUFDO0lBQ0wsQ0FBQyxFQUFFO1FBQUNOLE9BQU87UUFBRUcsY0FBYztRQUFFRyxlQUFlO0tBQUMsQ0FBQztJQUU5Qyx5QkFBeUI7SUFDekIsSUFBTVEsc0JBQXNCLEdBQUduQixrREFBVzttQkFBQyw2RkFBT29CLEdBQVcsRUFBSztnQkFPcERDLGdCQUFnQixFQVdSQyxPQUFPLEVBUUhDLFNBQVMsRUFLZFgsS0FBSyxFQUdKVyxVQUFTLEVBVWJBLFVBQVM7Ozs7d0JBM0N2QixJQUFJLENBQUNsQixPQUFPLElBQUlFLGVBQWUsQ0FBQ2lCLE9BQU8sRUFBRTs7MEJBQU87NkJBRTVDaEIsY0FBYyxFQUFFLEVBQWhCQTs7OzBCQUFnQjt3QkFDaEJpQixPQUFPLENBQUNDLEdBQUcsQ0FBQyw2QkFBa0IsRUFBRTs0QkFBRUMsU0FBUyxFQUFFUCxHQUFHO3lCQUFFLENBQUMsQ0FBQzt3QkFHOUNDLGdCQUFnQixHQUFHRCxHQUFHLENBQUNRLEtBQUsscUJBQXFCLElBQUlSLEdBQUcsS0FBSyxHQUFHLENBQUM7NkJBRW5FQyxnQkFBZ0IsRUFBaEJBOzs7MEJBQWdCO3dCQUNoQixjQUFjO3dCQUNkSSxPQUFPLENBQUNDLEdBQUcsQ0FBQywwQkFBZSxDQUFDLENBQUM7d0JBQzdCbkIsZUFBZSxDQUFDaUIsT0FBTyxHQUFHLElBQUksQ0FBQzt3QkFFL0IsU0FBUzt3QkFDVGxCLE1BQU0sQ0FBQ3VCLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLGtCQUFrQixFQUFFLElBQUlDLEtBQUssQ0FBQyxpQ0FBaUMsQ0FBQyxFQUFFWCxHQUFHLENBQUMsQ0FBQzs7Ozs7Ozs7O3dCQUd0RTs7NEJBQU1ULGVBQWUsRUFBRTswQkFBQTs7d0JBQWpDVyxPQUFPLEdBQUcsYUFBdUI7d0JBQ3ZDZixlQUFlLENBQUNpQixPQUFPLEdBQUcsS0FBSyxDQUFDO3dCQUVoQyxJQUFJRixPQUFPLEVBQUU7NEJBQ1RHLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDBCQUFlLENBQUMsQ0FBQzs0QkFDN0JwQixNQUFNLENBQUMwQixJQUFJLENBQUNaLEdBQUcsQ0FBQyxDQUFDO3dCQUNyQixPQUFPOzRCQUNISyxPQUFPLENBQUNDLEdBQUcsQ0FBQywwQkFBZSxDQUFDLENBQUM7NEJBQ3ZCSCxTQUFTLEdBQUdkLE1BQU0sQ0FBQ3dCLE9BQU8sQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDOzRCQUNuRCxJQUFJVixTQUFTLEVBQUU7Z0NBQ1hqQixNQUFNLENBQUMwQixJQUFJLENBQUNaLEdBQUcsQ0FBQyxDQUFDOzRCQUNyQixDQUFDO3dCQUNMLENBQUM7Ozs7Ozt3QkFDSVIsS0FBSzt3QkFDVmEsT0FBTyxDQUFDYixLQUFLLENBQUMsc0JBQVcsRUFBRUEsS0FBSyxDQUFDLENBQUM7d0JBQ2xDTCxlQUFlLENBQUNpQixPQUFPLEdBQUcsS0FBSyxDQUFDO3dCQUMxQkQsVUFBUyxHQUFHZCxNQUFNLENBQUN3QixPQUFPLENBQUMsZ0JBQWdCLENBQUMsQ0FBQzt3QkFDbkQsSUFBSVYsVUFBUyxFQUFFOzRCQUNYakIsTUFBTSxDQUFDMEIsSUFBSSxDQUFDWixHQUFHLENBQUMsQ0FBQzt3QkFDckIsQ0FBQzs7Ozs7Ozs7Ozs7d0JBR0wsYUFBYTt3QkFDYkssT0FBTyxDQUFDQyxHQUFHLENBQUMsMkJBQWdCLENBQUMsQ0FBQzt3QkFDOUJwQixNQUFNLENBQUN1QixNQUFNLENBQUNDLElBQUksQ0FBQyxrQkFBa0IsRUFBRSxJQUFJQyxLQUFLLENBQUMsNEJBQTRCLENBQUMsRUFBRVgsR0FBRyxDQUFDLENBQUM7d0JBRS9FRyxVQUFTLEdBQUdkLE1BQU0sQ0FBQ3dCLE9BQU8sQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDOzZCQUNqRFYsVUFBUyxFQUFUQTs7OzBCQUFTO3dCQUNURSxPQUFPLENBQUNDLEdBQUcsQ0FBQyx1QkFBWSxDQUFDLENBQUM7d0JBQzFCcEIsTUFBTSxDQUFDMEIsSUFBSSxDQUFDWixHQUFHLENBQUMsQ0FBQzs7Ozs7O3dCQUVqQkssT0FBTyxDQUFDQyxHQUFHLENBQUMsNEJBQWlCLENBQUMsQ0FBQzt3QkFDL0I7OzRCQUFNZixlQUFlLEVBQUU7MEJBQUE7O3dCQUF2QixhQUF1QixDQUFDOzs7Ozs7OztRQUl4QyxDQUFDO3dCQXREaURTLEdBQVc7OztTQXNEMUQ7UUFBQ2YsT0FBTztRQUFFRyxjQUFjO1FBQUVHLGVBQWU7UUFBRUwsTUFBTTtLQUFDLENBQUM7SUFJdERQLGdEQUFTLENBQUMsV0FBTTtRQUNaLElBQUksQ0FBQ00sT0FBTyxFQUFFLE9BQU87UUFFckJJLE1BQU0sQ0FBQ3lCLGdCQUFnQixDQUFDLGNBQWMsRUFBRXBCLGtCQUFrQixDQUFDLENBQUM7UUFFNUQsT0FBTyxXQUFNO1lBQ1RMLE1BQU0sQ0FBQzBCLG1CQUFtQixDQUFDLGNBQWMsRUFBRXJCLGtCQUFrQixDQUFDLENBQUM7UUFDbkUsQ0FBQyxDQUFDO0lBQ04sQ0FBQyxFQUFFO1FBQUNULE9BQU87UUFBRVMsa0JBQWtCO0tBQUMsQ0FBQyxDQUFDO0lBRWxDZixnREFBUyxDQUFDLFdBQU07UUFDWixJQUFJLENBQUNNLE9BQU8sRUFBRSxPQUFPO1FBRXJCQyxNQUFNLENBQUN1QixNQUFNLENBQUNPLEVBQUUsQ0FBQyxrQkFBa0IsRUFBRWpCLHNCQUFzQixDQUFDLENBQUM7UUFDN0QsT0FBTyxXQUFNO1lBQ1RiLE1BQU0sQ0FBQ3VCLE1BQU0sQ0FBQ1EsR0FBRyxDQUFDLGtCQUFrQixFQUFFbEIsc0JBQXNCLENBQUMsQ0FBQztRQUNsRSxDQUFDLENBQUM7SUFDTixDQUFDLEVBQUU7UUFBQ2QsT0FBTztRQUFFYyxzQkFBc0I7UUFBRWIsTUFBTSxDQUFDdUIsTUFBTTtLQUFDLENBQUMsQ0FBQztJQUVyRCxPQUFPO1FBQ0hyQixjQUFjLEVBQWRBLGNBQWM7UUFDZEcsZUFBZSxFQUFmQSxlQUFlO0tBQ2xCLENBQUM7QUFDTixDQUFDO0FBRUQsK0RBQWVSLGtCQUFrQixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2xpYnMvd2ViL2hvb2tzL3VzZS1hdXRvLXNhdmUtb24tbGVhdmUudHM/YjkyYiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEF1dG8gU2F2ZSBvbiBMZWF2ZSBIb29rXG4gKlxuICogQ29weXJpZ2h0IChjKSAyMDI1IHdheWNhYW5cbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgTGljZW5zZVxuICpcbiAqIFBlcm1pc3Npb24gaXMgaGVyZWJ5IGdyYW50ZWQsIGZyZWUgb2YgY2hhcmdlLCB0byBhbnkgcGVyc29uIG9idGFpbmluZyBhIGNvcHlcbiAqIG9mIHRoaXMgc29mdHdhcmUgYW5kIGFzc29jaWF0ZWQgZG9jdW1lbnRhdGlvbiBmaWxlcyAodGhlIFwiU29mdHdhcmVcIiksIHRvIGRlYWxcbiAqIGluIHRoZSBTb2Z0d2FyZSB3aXRob3V0IHJlc3RyaWN0aW9uLCBpbmNsdWRpbmcgd2l0aG91dCBsaW1pdGF0aW9uIHRoZSByaWdodHNcbiAqIHRvIHVzZSwgY29weSwgbW9kaWZ5LCBtZXJnZSwgcHVibGlzaCwgZGlzdHJpYnV0ZSwgc3VibGljZW5zZSwgYW5kL29yIHNlbGxcbiAqIGNvcGllcyBvZiB0aGUgU29mdHdhcmUsIGFuZCB0byBwZXJtaXQgcGVyc29ucyB0byB3aG9tIHRoZSBTb2Z0d2FyZSBpc1xuICogZnVybmlzaGVkIHRvIGRvIHNvLCBzdWJqZWN0IHRvIHRoZSBmb2xsb3dpbmcgY29uZGl0aW9uczpcbiAqXG4gKiBUaGUgYWJvdmUgY29weXJpZ2h0IG5vdGljZSBhbmQgdGhpcyBwZXJtaXNzaW9uIG5vdGljZSBzaGFsbCBiZSBpbmNsdWRlZCBpbiBhbGxcbiAqIGNvcGllcyBvciBzdWJzdGFudGlhbCBwb3J0aW9ucyBvZiB0aGUgU29mdHdhcmUuXG4gKi9cblxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VDYWxsYmFjaywgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInO1xuXG5pbnRlcmZhY2UgVXNlQXV0b1NhdmVPbkxlYXZlT3B0aW9ucyB7XG4gICAgZW5hYmxlZD86IGJvb2xlYW47XG59XG5cbmNvbnN0IHVzZUF1dG9TYXZlT25MZWF2ZSA9IChvcHRpb25zOiBVc2VBdXRvU2F2ZU9uTGVhdmVPcHRpb25zID0ge30pID0+IHtcbiAgICBjb25zdCB7IGVuYWJsZWQgPSB0cnVlIH0gPSBvcHRpb25zO1xuICAgIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICAgIGNvbnN0IGlzQXV0b1NhdmluZ1JlZiA9IHVzZVJlZihmYWxzZSk7XG5cblxuICAgIGNvbnN0IHNob3VsZEF1dG9TYXZlID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgKHdpbmRvdyBhcyBhbnkpLnNhdmVCdXR0b25TdGF0dXMpIHtcbiAgICAgICAgICAgIHJldHVybiAod2luZG93IGFzIGFueSkuc2F2ZUJ1dHRvblN0YXR1cyA9PT0gJ3NhdmUnO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9LCBbXSk7XG5cbiAgICBjb25zdCBwZXJmb3JtQXV0b1NhdmUgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiAod2luZG93IGFzIGFueSkuc2F2ZUJ1dHRvbkF1dG9TYXZlKSB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGF3YWl0ICh3aW5kb3cgYXMgYW55KS5zYXZlQnV0dG9uQXV0b1NhdmUoKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9LCBbXSk7XG5cbiAgICAvLyDpobXpnaLlhbPpl60v5Yi35paw5aSE55CGIC0g5by556qX5o+Q56S65py65Yi2XG4gICAgY29uc3QgaGFuZGxlQmVmb3JlVW5sb2FkID0gdXNlQ2FsbGJhY2soKGV2ZW50OiBCZWZvcmVVbmxvYWRFdmVudCkgPT4ge1xuICAgICAgICBpZiAoIWVuYWJsZWQpIHJldHVybjtcblxuICAgICAgICBpZiAoc2hvdWxkQXV0b1NhdmUoKSkge1xuICAgICAgICAgICAgLy8g6Zi75q2i6buY6K6k6KGM5Li677yM5pi+56S656Gu6K6k5a+56K+d5qGGXG4gICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgZXZlbnQucmV0dXJuVmFsdWUgPSAn5oKo5pyJ5pyq5L+d5a2Y55qE5pu05pS544CC56Gu5a6a6KaB56a75byA5ZCX77yfJztcblxuICAgICAgICAgICAgLy8g5L2/55So5bu26L+f5qOA5rWL55So5oi36YCJ5oupXG4gICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgICAgICAvLyDlpoLmnpzog73miafooYzliLDov5nph4zvvIzor7TmmI7nlKjmiLfpgInmi6nkuoZcIuWPlua2iFwi77yM6Ieq5Yqo5L+d5a2YXG4gICAgICAgICAgICAgICAgcGVyZm9ybUF1dG9TYXZlKCk7XG4gICAgICAgICAgICB9LCAxMDApO1xuXG4gICAgICAgICAgICByZXR1cm4gJ+aCqOacieacquS/neWtmOeahOabtOaUueOAguehruWumuimgeemu+W8gOWQl++8nyc7XG4gICAgICAgIH1cbiAgICB9LCBbZW5hYmxlZCwgc2hvdWxkQXV0b1NhdmUsIHBlcmZvcm1BdXRvU2F2ZV0pO1xuXG4gICAgLy8g8J+UpyDph43mnoTvvJrnrJTorrDot7PovazlpITnkIYgLSDljLrliIbnrJTorrBJROWPmOWMllxuICAgIGNvbnN0IGhhbmRsZVJvdXRlQ2hhbmdlU3RhcnQgPSB1c2VDYWxsYmFjayhhc3luYyAodXJsOiBzdHJpbmcpID0+IHtcbiAgICAgICAgaWYgKCFlbmFibGVkIHx8IGlzQXV0b1NhdmluZ1JlZi5jdXJyZW50KSByZXR1cm47XG5cbiAgICAgICAgaWYgKHNob3VsZEF1dG9TYXZlKCkpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SnIOajgOa1i+WIsOi3r+eUsei3s+i9rO+8jOacieacquS/neWtmOWGheWuuScsIHsgdGFyZ2V0VXJsOiB1cmwgfSk7XG5cbiAgICAgICAgICAgIC8vIOajgOafpeaYr+WQpuaYr+eslOiusElE5Y+Y5YyW77yI56yU6K6w6Lez6L2s77yJXG4gICAgICAgICAgICBjb25zdCBpc05vdGVOYXZpZ2F0aW9uID0gdXJsLm1hdGNoKC9eXFwvW2EtekEtWjAtOS1dKyQvKSB8fCB1cmwgPT09ICcvJztcblxuICAgICAgICAgICAgaWYgKGlzTm90ZU5hdmlnYXRpb24pIHtcbiAgICAgICAgICAgICAgICAvLyDnrJTorrDot7PovazvvJrnm7TmjqXoh6rliqjkv53lrZhcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UpyDnrJTorrDot7PovazvvIzmiafooYzoh6rliqjkv53lrZgnKTtcbiAgICAgICAgICAgICAgICBpc0F1dG9TYXZpbmdSZWYuY3VycmVudCA9IHRydWU7XG5cbiAgICAgICAgICAgICAgICAvLyDpmLvmraLot6/nlLHot7PovaxcbiAgICAgICAgICAgICAgICByb3V0ZXIuZXZlbnRzLmVtaXQoJ3JvdXRlQ2hhbmdlRXJyb3InLCBuZXcgRXJyb3IoJ0F1dG8tc2F2aW5nIGJlZm9yZSByb3V0ZSBjaGFuZ2UnKSwgdXJsKTtcblxuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHN1Y2Nlc3MgPSBhd2FpdCBwZXJmb3JtQXV0b1NhdmUoKTtcbiAgICAgICAgICAgICAgICAgICAgaXNBdXRvU2F2aW5nUmVmLmN1cnJlbnQgPSBmYWxzZTtcblxuICAgICAgICAgICAgICAgICAgICBpZiAoc3VjY2Vzcykge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflKcg6Ieq5Yqo5L+d5a2Y5oiQ5Yqf77yM57un57ut6Lez6L2sJyk7XG4gICAgICAgICAgICAgICAgICAgICAgICByb3V0ZXIucHVzaCh1cmwpO1xuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflKcg6Ieq5Yqo5L+d5a2Y5aSx6LSl77yM6K+i6Zeu55So5oi3Jyk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb25maXJtZWQgPSB3aW5kb3cuY29uZmlybSgn6Ieq5Yqo5L+d5a2Y5aSx6LSl44CC5piv5ZCm5by65Yi256a75byA77yfJyk7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoY29uZmlybWVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcm91dGVyLnB1c2godXJsKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ/CflKcg6Ieq5Yqo5L+d5a2Y5Ye66ZSZOicsIGVycm9yKTtcbiAgICAgICAgICAgICAgICAgICAgaXNBdXRvU2F2aW5nUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgY29uZmlybWVkID0gd2luZG93LmNvbmZpcm0oJ+iHquWKqOS/neWtmOWHuumUmeOAguaYr+WQpuW8uuWItuemu+W8gO+8nycpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoY29uZmlybWVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByb3V0ZXIucHVzaCh1cmwpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAvLyDpnZ7nrJTorrDot7PovazvvJrlvLnnqpfmj5DnpLpcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UpyDpnZ7nrJTorrDot7PovazvvIzmmL7npLrlvLnnqpfmj5DnpLonKTtcbiAgICAgICAgICAgICAgICByb3V0ZXIuZXZlbnRzLmVtaXQoJ3JvdXRlQ2hhbmdlRXJyb3InLCBuZXcgRXJyb3IoJ1VzZXIgY29uZmlybWF0aW9uIHJlcXVpcmVkJyksIHVybCk7XG5cbiAgICAgICAgICAgICAgICBjb25zdCBjb25maXJtZWQgPSB3aW5kb3cuY29uZmlybSgn5oKo5pyJ5pyq5L+d5a2Y55qE5pu05pS544CC56Gu5a6a6KaB56a75byA5ZCX77yfJyk7XG4gICAgICAgICAgICAgICAgaWYgKGNvbmZpcm1lZCkge1xuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UpyDnlKjmiLfnoa7orqTlvLrliLbnprvlvIAnKTtcbiAgICAgICAgICAgICAgICAgICAgcm91dGVyLnB1c2godXJsKTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UpyDnlKjmiLflj5bmtojnprvlvIDvvIzmiafooYzoh6rliqjkv53lrZgnKTtcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgcGVyZm9ybUF1dG9TYXZlKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfSwgW2VuYWJsZWQsIHNob3VsZEF1dG9TYXZlLCBwZXJmb3JtQXV0b1NhdmUsIHJvdXRlcl0pO1xuXG5cblxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmICghZW5hYmxlZCkgcmV0dXJuO1xuXG4gICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdiZWZvcmV1bmxvYWQnLCBoYW5kbGVCZWZvcmVVbmxvYWQpO1xuXG4gICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignYmVmb3JldW5sb2FkJywgaGFuZGxlQmVmb3JlVW5sb2FkKTtcbiAgICAgICAgfTtcbiAgICB9LCBbZW5hYmxlZCwgaGFuZGxlQmVmb3JlVW5sb2FkXSk7XG5cbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBpZiAoIWVuYWJsZWQpIHJldHVybjtcblxuICAgICAgICByb3V0ZXIuZXZlbnRzLm9uKCdyb3V0ZUNoYW5nZVN0YXJ0JywgaGFuZGxlUm91dGVDaGFuZ2VTdGFydCk7XG4gICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICByb3V0ZXIuZXZlbnRzLm9mZigncm91dGVDaGFuZ2VTdGFydCcsIGhhbmRsZVJvdXRlQ2hhbmdlU3RhcnQpO1xuICAgICAgICB9O1xuICAgIH0sIFtlbmFibGVkLCBoYW5kbGVSb3V0ZUNoYW5nZVN0YXJ0LCByb3V0ZXIuZXZlbnRzXSk7XG5cbiAgICByZXR1cm4ge1xuICAgICAgICBzaG91bGRBdXRvU2F2ZSxcbiAgICAgICAgcGVyZm9ybUF1dG9TYXZlLFxuICAgIH07XG59O1xuXG5leHBvcnQgZGVmYXVsdCB1c2VBdXRvU2F2ZU9uTGVhdmU7XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJ1c2VSZWYiLCJ1c2VSb3V0ZXIiLCJ1c2VBdXRvU2F2ZU9uTGVhdmUiLCJvcHRpb25zIiwiZW5hYmxlZCIsInJvdXRlciIsImlzQXV0b1NhdmluZ1JlZiIsInNob3VsZEF1dG9TYXZlIiwid2luZG93Iiwic2F2ZUJ1dHRvblN0YXR1cyIsInBlcmZvcm1BdXRvU2F2ZSIsImVycm9yIiwic2F2ZUJ1dHRvbkF1dG9TYXZlIiwiaGFuZGxlQmVmb3JlVW5sb2FkIiwiZXZlbnQiLCJwcmV2ZW50RGVmYXVsdCIsInJldHVyblZhbHVlIiwic2V0VGltZW91dCIsImhhbmRsZVJvdXRlQ2hhbmdlU3RhcnQiLCJ1cmwiLCJpc05vdGVOYXZpZ2F0aW9uIiwic3VjY2VzcyIsImNvbmZpcm1lZCIsImN1cnJlbnQiLCJjb25zb2xlIiwibG9nIiwidGFyZ2V0VXJsIiwibWF0Y2giLCJldmVudHMiLCJlbWl0IiwiRXJyb3IiLCJwdXNoIiwiY29uZmlybSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwib24iLCJvZmYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./libs/web/hooks/use-auto-save-on-leave.ts\n"));

/***/ })

});