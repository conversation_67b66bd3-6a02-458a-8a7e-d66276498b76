"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/hooks/use-auto-save-on-leave.ts":
/*!**************************************************!*\
  !*** ./libs/web/hooks/use-auto-save-on-leave.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Auto Save on Leave Hook\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\n\n\nvar useAutoSaveOnLeave = function() {\n    var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _enabled = options.enabled, enabled = _enabled === void 0 ? true : _enabled;\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var isAutoSavingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    var shouldAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if ( true && window.saveButtonStatus) {\n            var status = window.saveButtonStatus;\n            return status === \"save\";\n        }\n        return false;\n    }, []);\n    var performAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function() {\n        var error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!( true && window.saveButtonAutoSave)) return [\n                        3,\n                        4\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        window.saveButtonAutoSave()\n                    ];\n                case 2:\n                    _state.sent();\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), []);\n    // 页面关闭/刷新处理 - 弹窗提示机制\n    var handleBeforeUnload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        if (!enabled) return;\n        if (shouldAutoSave()) {\n            console.log(\"\\uD83D\\uDD27 页面关闭/刷新，有未保存内容，显示弹窗\");\n            // 显示确认对话框\n            var message = \"您有未保存的更改。确定要离开吗？\";\n            event.returnValue = message;\n            // 使用延迟检测用户选择\n            setTimeout(function() {\n                // 如果能执行到这里，说明用户选择了\"取消\"，自动保存\n                console.log(\"\\uD83D\\uDD27 用户选择取消，执行自动保存\");\n                performAutoSave();\n            }, 100);\n            return message;\n        }\n    }, [\n        enabled,\n        shouldAutoSave,\n        performAutoSave\n    ]);\n    // 笔记跳转处理 - 区分笔记ID变化\n    var handleRouteChangeStart = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function(url) {\n            var shouldSave, isNoteNavigation;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n                if (!enabled || isAutoSavingRef.current) return [\n                    2\n                ];\n                shouldSave = shouldAutoSave();\n                console.log(\"\\uD83D\\uDD27 路由变化检测:\", {\n                    url: url,\n                    shouldSave: shouldSave\n                });\n                if (shouldSave) {\n                    isNoteNavigation = url.match(/^\\/[a-zA-Z0-9-]+(\\?.*)?$/) || url === \"/\" || url.includes(\"?new\");\n                    console.log(\"\\uD83D\\uDD27 路由类型:\", {\n                        isNoteNavigation: isNoteNavigation,\n                        url: url\n                    });\n                    // 阻止默认路由跳转\n                    throw new Error(\"Auto-saving before route change\");\n                }\n                return [\n                    2\n                ];\n            });\n        });\n        return function(url) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        enabled,\n        shouldAutoSave\n    ]);\n    // 单独处理自动保存和跳转\n    var handleAutoSaveAndNavigate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function(url) {\n            var isNoteNavigation, success, confirmed, error, confirmed1, confirmed2;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (isAutoSavingRef.current) return [\n                            2\n                        ];\n                        isNoteNavigation = url.match(/^\\/[a-zA-Z0-9-]+(\\?.*)?$/) || url === \"/\" || url.includes(\"?new\");\n                        if (!isNoteNavigation) return [\n                            3,\n                            5\n                        ];\n                        // 笔记跳转：直接自动保存\n                        console.log(\"\\uD83D\\uDD27 笔记跳转，开始自动保存\");\n                        isAutoSavingRef.current = true;\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            performAutoSave()\n                        ];\n                    case 2:\n                        success = _state.sent();\n                        console.log(\"\\uD83D\\uDD27 自动保存结果:\", success);\n                        if (success) {\n                            console.log(\"\\uD83D\\uDD27 自动保存成功，继续跳转\");\n                            isAutoSavingRef.current = false;\n                            router.push(url);\n                        } else {\n                            isAutoSavingRef.current = false;\n                            confirmed = window.confirm(\"自动保存失败。是否强制离开？\");\n                            if (confirmed) {\n                                router.push(url);\n                            }\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"自动保存出错:\", error);\n                        isAutoSavingRef.current = false;\n                        confirmed1 = window.confirm(\"自动保存出错。是否强制离开？\");\n                        if (confirmed1) {\n                            router.push(url);\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            3,\n                            8\n                        ];\n                    case 5:\n                        confirmed2 = window.confirm(\"您有未保存的更改。确定要离开吗？\");\n                        if (!confirmed2) return [\n                            3,\n                            6\n                        ];\n                        router.push(url);\n                        return [\n                            3,\n                            8\n                        ];\n                    case 6:\n                        return [\n                            4,\n                            performAutoSave()\n                        ];\n                    case 7:\n                        _state.sent();\n                        _state.label = 8;\n                    case 8:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(url) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        performAutoSave,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return function() {\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        enabled,\n        handleBeforeUnload\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        router.events.on(\"routeChangeStart\", handleRouteChangeStart);\n        // 设置全局函数供外部调用\n        if (true) {\n            window.handleAutoSaveAndNavigate = handleAutoSaveAndNavigate;\n        }\n        return function() {\n            router.events.off(\"routeChangeStart\", handleRouteChangeStart);\n            if (true) {\n                delete window.handleAutoSaveAndNavigate;\n            }\n        };\n    }, [\n        enabled,\n        handleRouteChangeStart,\n        handleAutoSaveAndNavigate,\n        router.events\n    ]);\n    return {\n        shouldAutoSave: shouldAutoSave,\n        performAutoSave: performAutoSave,\n        handleAutoSaveAndNavigate: handleAutoSaveAndNavigate\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useAutoSaveOnLeave);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/hooks/use-auto-save-on-leave.ts\n"));

/***/ })

});