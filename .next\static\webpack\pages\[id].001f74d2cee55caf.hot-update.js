"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/lexical-main-editor.tsx":
/*!***************************************************!*\
  !*** ./components/editor/lexical-main-editor.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_without_properties_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_object_without_properties.mjs */ \"./node_modules/@swc/helpers/src/_object_without_properties.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _edit_title__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./edit-title */ \"./components/editor/edit-title.tsx\");\n/* harmony import */ var _lexical_editor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lexical-editor */ \"./components/editor/lexical-editor.tsx\");\n/* harmony import */ var _backlinks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./backlinks */ \"./components/editor/backlinks.tsx\");\n/* harmony import */ var libs_web_state_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/state/ui */ \"./libs/web/state/ui.ts\");\n/* harmony import */ var libs_shared_meta__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/shared/meta */ \"./libs/shared/meta.ts\");\n/* harmony import */ var libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/state/lexical-editor */ \"./libs/web/state/lexical-editor.ts\");\n/**\n * Lexical Main Editor Component\n * Migrated from TipTap to Lexical\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nvar LexicalMainEditor = function(_param) {\n    var className = _param.className, note = _param.note, isPreview = _param.isPreview, props = (0,_swc_helpers_src_object_without_properties_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_param, [\n        \"className\",\n        \"note\",\n        \"isPreview\"\n    ]);\n    var ref, ref1;\n    _s();\n    var ref2 = libs_web_state_ui__WEBPACK_IMPORTED_MODULE_4__[\"default\"].useContainer(), settings = ref2.settings.settings;\n    var ref3 = libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useContainer(), onSearchLink = ref3.onSearchLink, onCreateLink = ref3.onCreateLink, onClickLink = ref3.onClickLink, onHoverLink = ref3.onHoverLink, onEditorChange = ref3.onEditorChange, editorEl = ref3.editorEl, editorNote = ref3.note;\n    var ref4;\n    var currentEditorSize = (ref4 = note === null || note === void 0 ? void 0 : note.editorsize) !== null && ref4 !== void 0 ? ref4 : settings.editorsize;\n    var editorWidthClass;\n    switch(currentEditorSize){\n        case libs_shared_meta__WEBPACK_IMPORTED_MODULE_5__.EDITOR_SIZE.SMALL:\n            editorWidthClass = \"max-w-400\";\n            break;\n        case libs_shared_meta__WEBPACK_IMPORTED_MODULE_5__.EDITOR_SIZE.LARGE:\n            editorWidthClass = \"max-w-4xl\";\n            break;\n        case libs_shared_meta__WEBPACK_IMPORTED_MODULE_5__.EDITOR_SIZE.FULL:\n            editorWidthClass = \"max-w-full mx-4\";\n            break;\n        default:\n            editorWidthClass = \"max-w-400\";\n            break;\n    }\n    var articleClassName = className || \"pt-16 md:pt-40 px-6 m-auto h-full \".concat(editorWidthClass);\n    // 调试信息\n    console.log(\"\\uD83D\\uDD27 LexicalMainEditor 渲染:\", {\n        hasEditorNote: !!editorNote,\n        noteId: editorNote === null || editorNote === void 0 ? void 0 : editorNote.id,\n        contentLength: (editorNote === null || editorNote === void 0 ? void 0 : (ref = editorNote.content) === null || ref === void 0 ? void 0 : ref.length) || 0,\n        isContentJson: (editorNote === null || editorNote === void 0 ? void 0 : (ref1 = editorNote.content) === null || ref1 === void 0 ? void 0 : ref1.startsWith(\"{\")) || false,\n        hasOnChange: !!onEditorChange,\n        timestamp: new Date().toISOString()\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: articleClassName,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_edit_title__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                readOnly: props.readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-main-editor.tsx\",\n                lineNumber: 69,\n                columnNumber: 13\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n                ref: editorEl,\n                value: editorNote === null || editorNote === void 0 ? void 0 : editorNote.content,\n                onChange: onEditorChange,\n                onCreateLink: onCreateLink,\n                onSearchLink: onSearchLink,\n                onClickLink: onClickLink,\n                onHoverLink: onHoverLink,\n                isPreview: isPreview,\n                className: \"px-4 md:px-0\"\n            }, props), void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-main-editor.tsx\",\n                lineNumber: 70,\n                columnNumber: 13\n            }, _this),\n            !isPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_backlinks__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-main-editor.tsx\",\n                lineNumber: 82,\n                columnNumber: 28\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-main-editor.tsx\",\n        lineNumber: 68,\n        columnNumber: 9\n    }, _this);\n};\n_s(LexicalMainEditor, \"l1sqHPD4RZHXuhpJbqweEzzdwUE=\", false, function() {\n    return [\n        libs_web_state_ui__WEBPACK_IMPORTED_MODULE_4__[\"default\"].useContainer,\n        libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useContainer\n    ];\n});\n_c = LexicalMainEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalMainEditor);\nvar _c;\n$RefreshReg$(_c, \"LexicalMainEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-main-editor.tsx\n"));

/***/ })

});