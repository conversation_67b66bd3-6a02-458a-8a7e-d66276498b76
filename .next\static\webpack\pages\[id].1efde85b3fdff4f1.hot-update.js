"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/lexical-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/lexical-editor.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"./node_modules/@lexical/react/LexicalComposer.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"./node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"./node_modules/@lexical/react/LexicalContentEditable.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"./node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"./node_modules/@lexical/react/LexicalAutoFocusPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"./node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @lexical/react/LexicalLinkPlugin */ \"./node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @lexical/react/LexicalListPlugin */ \"./node_modules/@lexical/react/LexicalListPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @lexical/react/LexicalMarkdownShortcutPlugin */ \"./node_modules/@lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs\");\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @lexical/markdown */ \"./node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"./node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/rich-text */ \"./node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/code */ \"./node_modules/@lexical/code/LexicalCode.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-div-100vh */ \"./node_modules/react-div-100vh/dist/esm/index.js\");\n/* harmony import */ var libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/hooks/use-mounted */ \"./libs/web/hooks/use-mounted.ts\");\n/* harmony import */ var libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/hooks/use-i18n */ \"./libs/web/hooks/use-i18n.tsx\");\n/* harmony import */ var _plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plugins/slash-commands-plugin */ \"./components/editor/plugins/slash-commands-plugin.tsx\");\n/* harmony import */ var _plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./plugins/floating-toolbar-plugin */ \"./components/editor/plugins/floating-toolbar-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @lexical/react/LexicalCheckListPlugin */ \"./node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs\");\n/* harmony import */ var _plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./plugins/highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var _plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./plugins/image-plugin */ \"./components/editor/plugins/image-plugin.tsx\");\n/* harmony import */ var _plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./plugins/ime-plugin */ \"./components/editor/plugins/ime-plugin.tsx\");\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"./node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRulePlugin */ \"./node_modules/@lexical/react/LexicalHorizontalRulePlugin.dev.mjs\");\n/**\n * Lexical Editor Component\n * Migrated from TipTap to Lexical for better performance and modern architecture\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import custom plugins and nodes\n\n\n\n\n\n\n\n\n\nvar theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\",\n    quote: \"editor-quote\",\n    heading: {\n        h1: \"editor-heading-h1\",\n        h2: \"editor-heading-h2\",\n        h3: \"editor-heading-h3\",\n        h4: \"editor-heading-h4\",\n        h5: \"editor-heading-h5\",\n        h6: \"editor-heading-h6\"\n    },\n    list: {\n        nested: {\n            listitem: \"editor-nested-listitem\"\n        },\n        ol: \"editor-list-ol\",\n        ul: \"editor-list-ul\",\n        listitem: \"editor-listitem\",\n        // 按深度定义不同的列表样式 - 这是关键！\n        olDepth: [\n            \"editor-list-ol-1\",\n            \"editor-list-ol-2\",\n            \"editor-list-ol-3\",\n            \"editor-list-ol-4\",\n            \"editor-list-ol-5\" // I, II, III...\n        ],\n        ulDepth: [\n            \"editor-list-ul-1\",\n            \"editor-list-ul-2\",\n            \"editor-list-ul-3\",\n            \"editor-list-ul-4\",\n            \"editor-list-ul-5\" // ‣\n        ],\n        checklist: \"PlaygroundEditorTheme__checklist\",\n        listitemChecked: \"PlaygroundEditorTheme__listItemChecked\",\n        listitemUnchecked: \"PlaygroundEditorTheme__listItemUnchecked\"\n    },\n    // Lexical 0.32.1 原生缩进支持\n    indent: \"lexical-indent\",\n    image: \"editor-image\",\n    link: \"editor-link\",\n    text: {\n        bold: \"editor-text-bold\",\n        italic: \"editor-text-italic\",\n        overflowed: \"editor-text-overflowed\",\n        hashtag: \"editor-text-hashtag\",\n        underline: \"editor-text-underline\",\n        strikethrough: \"editor-text-strikethrough\",\n        underlineStrikethrough: \"editor-text-underlineStrikethrough\",\n        code: \"editor-text-code\",\n        highlight: \"editor-text-highlight\"\n    },\n    code: \"editor-code\",\n    codeHighlight: {\n        atrule: \"editor-tokenAttr\",\n        attr: \"editor-tokenAttr\",\n        boolean: \"editor-tokenProperty\",\n        builtin: \"editor-tokenSelector\",\n        cdata: \"editor-tokenComment\",\n        char: \"editor-tokenSelector\",\n        class: \"editor-tokenFunction\",\n        \"class-name\": \"editor-tokenFunction\",\n        comment: \"editor-tokenComment\",\n        constant: \"editor-tokenProperty\",\n        deleted: \"editor-tokenProperty\",\n        doctype: \"editor-tokenComment\",\n        entity: \"editor-tokenOperator\",\n        function: \"editor-tokenFunction\",\n        important: \"editor-tokenVariable\",\n        inserted: \"editor-tokenSelector\",\n        keyword: \"editor-tokenAttr\",\n        namespace: \"editor-tokenVariable\",\n        number: \"editor-tokenProperty\",\n        operator: \"editor-tokenOperator\",\n        prolog: \"editor-tokenComment\",\n        property: \"editor-tokenProperty\",\n        punctuation: \"editor-tokenPunctuation\",\n        regex: \"editor-tokenVariable\",\n        selector: \"editor-tokenSelector\",\n        string: \"editor-tokenSelector\",\n        symbol: \"editor-tokenProperty\",\n        tag: \"editor-tokenProperty\",\n        url: \"editor-tokenOperator\",\n        variable: \"editor-tokenVariable\"\n    }\n};\nfunction Placeholder() {\n    _s();\n    var t = (0,libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"])().t;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: t(\"Start writing...\")\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 153,\n        columnNumber: 12\n    }, this);\n}\n_s(Placeholder, \"82N5KF9nLzZ6+2WH7KIjzIXRkLw=\", false, function() {\n    return [\n        libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Placeholder;\nvar LexicalEditor = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(_c1 = _s1(function(param, ref) {\n    var _readOnly = param.readOnly, readOnly = _readOnly === void 0 ? false : _readOnly, _value = param.value, value = _value === void 0 ? \"\" : _value, onChange = param.onChange, onClickLink = param.onClickLink, onHoverLink = param.onHoverLink, _className = param.className, className = _className === void 0 ? \"\" : _className;\n    _s1();\n    var _s = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n    var height = (0,react_div_100vh__WEBPACK_IMPORTED_MODULE_4__.use100vh)();\n    var mounted = (0,libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // 创建一个 ref 来存储编辑器实例\n    var editorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    var initialConfig = {\n        namespace: \"LexicalEditor\",\n        theme: theme,\n        onError: function onError(error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n            // Lexical原生的ListItemNode已经支持checkbox功能\n            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n        ],\n        editable: !readOnly,\n        // 设置初始内容\n        editorState: value ? undefined : null\n    };\n    // 创建自定义transformers，包含图片支持\n    var IMAGE_TRANSFORMER = {\n        dependencies: [\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode\n        ],\n        export: function(node) {\n            if (!(0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$isImageNode)(node)) {\n                return null;\n            }\n            return \"![\".concat(node.getAltText(), \"](\").concat(node.getSrc(), \")\");\n        },\n        regExp: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/,\n        replace: function(parentNode, children, match) {\n            var _match = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(match, 3), altText = _match[1], src = _match[2];\n            var imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$createImageNode)({\n                altText: altText,\n                src: src,\n                maxWidth: 800\n            });\n            children.forEach(function(child) {\n                return child.remove();\n            });\n            parentNode.append(imageNode);\n        },\n        type: \"element\"\n    };\n    // 创建自定义的下划线转换器，使用 <u>text</u> 语法\n    var UNDERLINE_TRANSFORMER = {\n        format: [\n            \"underline\"\n        ],\n        tag: \"<u>\",\n        type: \"text-format\"\n    };\n    // 移除段落缩进转换器 - 专注于列表缩进功能\n    // 段落缩进不是标准markdown语法，我们只处理列表缩进\n    // 创建水平分割线转换器\n    var HR_TRANSFORMER = {\n        dependencies: [\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode\n        ],\n        export: function(node) {\n            return (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$isHorizontalRuleNode)(node) ? \"---\" : null;\n        },\n        regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n        replace: function(parentNode, children, match, isImport) {\n            var line = (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$createHorizontalRuleNode)();\n            if (isImport || parentNode.getNextSibling() != null) {\n                parentNode.replace(line);\n            } else {\n                parentNode.insertBefore(line);\n            }\n            line.selectNext();\n        },\n        type: \"element\"\n    };\n    // 🔧 JSON格式保存 - 完全使用Lexical原生格式\n    // 使用标准的Lexical transformers\n    var customTransformers = (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(_lexical_markdown__WEBPACK_IMPORTED_MODULE_19__.TRANSFORMERS).concat([\n        // 添加我们的自定义转换器\n        HR_TRANSFORMER,\n        UNDERLINE_TRANSFORMER,\n        IMAGE_TRANSFORMER\n    ]);\n    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题\n    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(editorState, _editor, tags) {\n        if (onChange) {\n            // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新\n            if (tags.has(\"history-merge\") || tags.has(\"content-sync\")) {\n                return;\n            }\n            editorState.read(function() {\n                try {\n                    // 使用Lexical原生JSON格式保存，完美保持嵌套关系\n                    var editorStateJSON = JSON.stringify(editorState.toJSON());\n                    // 简单的内容变化检查（比较JSON字符串）\n                    if (editorStateJSON !== value) {\n                        onChange(function() {\n                            return editorStateJSON;\n                        });\n                    }\n                } catch (error) {\n                    console.error(\"Error in handleChange:\", error);\n                // 如果转换出错，保持原有内容不变\n                }\n            });\n        }\n    }, [\n        onChange,\n        value\n    ]);\n    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑\n    var ListExitPlugin = function() {\n        _s();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_20__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            return editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_21__.KEY_ENTER_COMMAND, function(event) {\n                var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$getSelection)();\n                if (!(0,lexical__WEBPACK_IMPORTED_MODULE_21__.$isRangeSelection)(selection)) {\n                    return false;\n                }\n                var anchorNode = selection.anchor.getNode();\n                // 检查是否在空的列表项中\n                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(anchorNode)) {\n                    var textContent = anchorNode.getTextContent().trim();\n                    if (textContent === \"\") {\n                        var listNode = anchorNode.getParent();\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(listNode)) {\n                            // 如果是空的列表项，退出列表\n                            event === null || event === void 0 ? void 0 : event.preventDefault();\n                            // 创建新段落并在列表后插入\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$createParagraphNode)();\n                            listNode.insertAfter(paragraph);\n                            // 删除空的列表项\n                            anchorNode.remove();\n                            // 选中新段落\n                            paragraph.select();\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            }, lexical__WEBPACK_IMPORTED_MODULE_21__.COMMAND_PRIORITY_HIGH);\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s(ListExitPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_20__.useLexicalComposerContext\n        ];\n    });\n    // 内容同步组件 - 模仿TipTap的方式\n    var ContentSyncPlugin = function() {\n        _s2();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_20__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (editor && value !== undefined && mounted) {\n                // 使用setTimeout来避免在渲染过程中调用flushSync\n                setTimeout(function() {\n                    if (value.trim()) {\n                        try {\n                            // 解析JSON格式的编辑器状态\n                            var editorStateData = JSON.parse(value);\n                            // 直接设置编辑器状态\n                            var newEditorState = editor.parseEditorState(editorStateData);\n                            editor.setEditorState(newEditorState);\n                        } catch (jsonError) {\n                            // JSON解析失败，创建空编辑器\n                            editor.update(function() {\n                                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$getRoot)();\n                                root.clear();\n                                var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$createParagraphNode)();\n                                root.append(paragraph);\n                            });\n                        }\n                    } else {\n                        // 空内容时清空并创建一个空段落\n                        editor.update(function() {\n                            var root = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$getRoot)();\n                            root.clear();\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$createParagraphNode)();\n                            root.append(paragraph);\n                        });\n                    }\n                }, 0);\n            }\n        }, [\n            editor,\n            value,\n            mounted\n        ]);\n        return null;\n    };\n    _s2(ContentSyncPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_20__.useLexicalComposerContext\n        ];\n    });\n    // 编辑器引用组件 - 获取编辑器实例\n    var EditorRefPlugin = function() {\n        _s3();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_20__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            editorRef.current = editor;\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s3(EditorRefPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_20__.useLexicalComposerContext\n        ];\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function() {\n        return {\n            focusAtEnd: function() {\n                // 实现焦点到编辑器末尾\n                if (editorRef.current) {\n                    editorRef.current.focus(function() {\n                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$getRoot)();\n                        var lastChild = root.getLastChild();\n                        if (lastChild) {\n                            lastChild.selectEnd();\n                        } else {\n                            root.selectEnd();\n                        }\n                    });\n                }\n            },\n            focusAtStart: function() {\n                // 实现焦点到编辑器开始\n                if (editorRef.current) {\n                    editorRef.current.focus(function() {\n                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$getRoot)();\n                        var firstChild = root.getFirstChild();\n                        if (firstChild) {\n                            firstChild.selectStart();\n                        } else {\n                            root.selectStart();\n                        }\n                    });\n                }\n            }\n        };\n    });\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"b6e8ec94f89652f1\",\n                [\n                    height ? height + \"px\" : \"100vh\"\n                ]\n            ]\n        ]) + \" \" + \"lexical-editor \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__.LexicalComposer, {\n                initialConfig: initialConfig,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"b6e8ec94f89652f1\",\n                            [\n                                height ? height + \"px\" : \"100vh\"\n                            ]\n                        ]\n                    ]) + \" \" + \"editor-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__.ContentEditable, {\n                                className: \"editor-input focus:outline-none w-full\",\n                                spellCheck: false\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__.LinkPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__.ListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__.CheckListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__.MarkdownShortcutPlugin, {\n                            transformers: customTransformers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__.HorizontalRulePlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            enabled: true,\n                            debug: \"development\" === \"development\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListExitPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditorRefPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSyncPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__.OnChangePlugin, {\n                            onChange: handleChange,\n                            ignoreHistoryMergeTagChange: true,\n                            ignoreSelectionChange: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 21\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                    lineNumber: 428,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                lineNumber: 427,\n                columnNumber: 13\n            }, _this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"b6e8ec94f89652f1\",\n                dynamic: [\n                    height ? height + \"px\" : \"100vh\"\n                ],\n                children: \".lexical-editor{position:relative}.editor-container{position:relative}.editor-input{outline:none;padding:1rem 0;min-height:-webkit-calc(\".concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:-moz-calc(\").concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:calc(\").concat(height ? height + \"px\" : \"100vh\", ' - 14rem);padding-bottom:10rem;width:100%;max-width:none;line-height:1.7;font-size:1rem;color:inherit;-webkit-spellcheck:false;-moz-spellcheck:false;-ms-spellcheck:false;spellcheck:false}.lexical-editor{--lexical-indent-base-value:32px}.lexical-indent{--lexical-indent-base-value:32px}.editor-input p[style*=\"margin-left\"],.editor-input h1[style*=\"margin-left\"],.editor-input h2[style*=\"margin-left\"],.editor-input h3[style*=\"margin-left\"],.editor-input h4[style*=\"margin-left\"],.editor-input h5[style*=\"margin-left\"],.editor-input h6[style*=\"margin-left\"],.editor-input li[style*=\"margin-left\"]{-webkit-transition:margin-left.2s ease;-moz-transition:margin-left.2s ease;-o-transition:margin-left.2s ease;transition:margin-left.2s ease}.editor-placeholder{color:#999;overflow:hidden;position:absolute;-o-text-overflow:ellipsis;text-overflow:ellipsis;top:1rem;left:0;font-size:1rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;pointer-events:none}.editor-paragraph{margin:1.2rem 0;line-height:1.8}.editor-heading-h1{font-size:2.8em;font-weight:bold;margin:1.5rem 0 1rem 0;line-height:1.2}.editor-heading-h2{font-size:2.2em;font-weight:bold;margin:1.4rem 0 .8rem 0;line-height:1.3}.editor-heading-h3{font-size:1.8em;font-weight:bold;margin:1.3rem 0 .6rem 0;line-height:1.4}.editor-heading-h4{font-size:1.5em;font-weight:bold;margin:1.2rem 0 .5rem 0;line-height:1.4}.editor-heading-h5{font-size:1.3em;font-weight:bold;margin:1.1rem 0 .4rem 0;line-height:1.5}.editor-heading-h6{font-size:1.2em;font-weight:bold;margin:1rem 0 .3rem 0;line-height:1.5}.editor-quote{margin:1rem 0;padding-left:1rem;border-left:4px solid#ccc;font-style:italic;color:#666}.editor-list-ol,.editor-list-ul{margin:1rem 0;padding-left:2rem}.editor-listitem{margin:.5rem 0}.editor-link{color:#3b82f6;text-decoration:underline;cursor:pointer}.editor-link:hover{color:#1d4ed8}.editor-text-bold{font-weight:bold}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.editor-text-strikethrough{text-decoration:line-through}.editor-text-code{background-color:#e4e4e7;color:black;padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:monospace;font-size:.9em}.editor-code{background-color:#e4e4e7;color:black;border:1px solid#e9ecef;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;padding:1rem;margin:1rem 0;font-family:\"Courier New\",Courier,monospace;font-size:.9em;line-height:1.4;overflow-x:auto}[data-theme=\"dark\"] .editor-text-code{background-color:#3f3f46;color:white}[data-theme=\"dark\"] .editor-code{background-color:#3f3f46;color:white;border-color:#4b5563}.editor-list-ol{list-style-type:decimal;margin:.5rem 0;padding-left:1.5rem}.editor-list-ul{list-style-type:disc;margin:.5rem 0;padding-left:1.5rem}.editor-listitem{margin:.4rem 0;line-height:1.8}.editor-nested-listitem{margin:.3rem 0}.editor-list-ol-1{list-style-type:decimal}.editor-list-ol-2{list-style-type:lower-alpha}.editor-list-ol-3{list-style-type:lower-roman}.editor-list-ol-4{list-style-type:upper-alpha}.editor-list-ol-5{list-style-type:upper-roman}.editor-list-ul-1{list-style-type:disc}.editor-list-ul-2{list-style-type:circle}.editor-list-ul-3{list-style-type:square}.editor-list-ul-4{list-style-type:disc}.editor-list-ul-5{list-style-type:circle}.task-list{list-style:none;padding-left:0;margin:1rem 0}.task-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;margin:.5rem 0;list-style:none}.task-checkbox{margin-right:.5rem;margin-top:.125rem;cursor:pointer;width:1rem;height:1rem;border:1px solid#d1d5db;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;background:white;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-appearance:none}.task-checkbox:checked{background-color:#3b82f6;border-color:#3b82f6;color:white}.task-checkbox:checked::after{content:\"✓\";font-size:.75rem;font-weight:bold;color:white}.task-content{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:1.7}.task-item[data-checked=\"true\"] .task-content{text-decoration:line-through;opacity:.6}.lexical-highlight,mark,.editor-text-highlight{background-color:#eab834!important;color:black!important;padding:.1rem .2rem;-webkit-border-radius:.125rem;-moz-border-radius:.125rem;border-radius:.125rem}.dark .lexical-highlight,.dark mark,.dark .editor-text-highlight,[data-theme=\"dark\"] .lexical-highlight,[data-theme=\"dark\"] mark,[data-theme=\"dark\"] .editor-text-highlight,html.dark .lexical-highlight,html.dark mark,html.dark .editor-text-highlight{background-color:#3185eb!important;color:white!important}.editor-image img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}@media(prefers-color-scheme:dark){.editor-placeholder{color:#6b7280}.editor-quote{border-left-color:#4b5563;color:#9ca3af}.editor-text-code{background-color:#374151;color:#f9fafb}.editor-code{background-color:#1f2937;border-color:#374151;color:#f9fafb}}.lexical-editor ul:not([data-lexical-list-type=\"check\"]) li::marker{color:#6b7280}.lexical-editor ol li::marker{color:#6b7280}.lexical-editor .PlaygroundEditorTheme__listItemChecked,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked{position:relative;margin-left:.5em;margin-right:.5em;padding-left:1.5em;padding-right:1.5em;list-style-type:none;outline:none;display:block;min-height:1.5em}.lexical-editor .PlaygroundEditorTheme__listItemChecked>*,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked>*{margin-left:.01em}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{content:\"\";width:.9em;height:.9em;top:50%;left:0;cursor:pointer;display:block;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover;position:absolute;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.lexical-editor .PlaygroundEditorTheme__listItemChecked{text-decoration:line-through}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before{-webkit-box-shadow:0 0 0 2px#a6cdfe;-moz-box-shadow:0 0 0 2px#a6cdfe;box-shadow:0 0 0 2px#a6cdfe;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before{border:1px solid#999;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{border:1px solid rgb(61,135,245);-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;background-color:#3d87f5;background-repeat:no-repeat}.lexical-editor .PlaygroundEditorTheme__listItemChecked:after{content:\"\";cursor:pointer;border-color:#fff;border-style:solid;position:absolute;display:block;top:45%;width:.2em;left:.35em;height:.4em;-webkit-transform:translateY(-50%)rotate(45deg);-moz-transform:translateY(-50%)rotate(45deg);-ms-transform:translateY(-50%)rotate(45deg);-o-transform:translateY(-50%)rotate(45deg);transform:translateY(-50%)rotate(45deg);border-width:0 .1em .1em 0}')\n            }, void 0, false, void 0, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 426,\n        columnNumber: 9\n    }, _this);\n}, \"oS0wBeFl4MEW/6I6OJbEwOv9UcA=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n})), \"oS0wBeFl4MEW/6I6OJbEwOv9UcA=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c2 = LexicalEditor;\nLexicalEditor.displayName = \"LexicalEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Placeholder\");\n$RefreshReg$(_c1, \"LexicalEditor$forwardRef\");\n$RefreshReg$(_c2, \"LexicalEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-editor.tsx\n"));

/***/ })

});