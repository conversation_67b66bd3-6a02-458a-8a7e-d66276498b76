"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/plugins/ime-plugin.tsx":
/*!**************************************************!*\
  !*** ./components/editor/plugins/ime-plugin.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ IMEPlugin; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var libs_web_utils_ime_state_manager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! libs/web/utils/ime-state-manager */ \"./libs/web/utils/ime-state-manager.ts\");\n/**\n * IME Plugin for Lexical\n * Provides better Chinese input method support\n */ \nvar _s = $RefreshSig$();\n\n\n\nfunction IMEPlugin() {\n    var ref = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, _enabled = ref.enabled, enabled = _enabled === void 0 ? true : _enabled, _debug = ref.debug, debug = _debug === void 0 ? false : _debug;\n    _s();\n    var ref1 = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_3__.useLexicalComposerContext)(), 1), editor = ref1[0];\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        var rootElement = editor.getRootElement();\n        if (!rootElement) return;\n        // 获取全局 IME 状态管理器\n        var imeStateManager = (0,libs_web_utils_ime_state_manager__WEBPACK_IMPORTED_MODULE_1__.getGlobalIMEStateManager)();\n        var isComposing = false;\n        var compositionData = \"\";\n        var handleCompositionStart = function(event) {\n            isComposing = true;\n            compositionData = \"\";\n            // 同步到全局状态管理器\n            imeStateManager.updateCompositionState(true, event.data);\n            if (debug) {\n                console.log(\"IME: Composition started\", event);\n            }\n        };\n        var handleCompositionUpdate = function(event) {\n            if (isComposing) {\n                compositionData = event.data || \"\";\n                // 同步到全局状态管理器\n                imeStateManager.updateCompositionState(true, event.data);\n                if (debug) {\n                    console.log(\"IME: Composition update\", event.data);\n                }\n            }\n        };\n        var handleCompositionEnd = function(event) {\n            isComposing = false;\n            compositionData = \"\";\n            // 同步到全局状态管理器\n            imeStateManager.updateCompositionState(false, event.data);\n            if (debug) {\n                console.log(\"IME: Composition ended\", event.data);\n            }\n        };\n        var handleBeforeInput = function(event) {\n            // Let composition events handle IME input\n            if (isComposing) {\n                if (debug) {\n                    console.log(\"IME: Blocking beforeinput during composition\", event);\n                }\n                return;\n            }\n        };\n        var handleInput = function(event) {\n            // Additional input handling if needed\n            if (debug && isComposing) {\n                console.log(\"IME: Input during composition\", event);\n            }\n        };\n        // Add event listeners\n        rootElement.addEventListener(\"compositionstart\", handleCompositionStart);\n        rootElement.addEventListener(\"compositionupdate\", handleCompositionUpdate);\n        rootElement.addEventListener(\"compositionend\", handleCompositionEnd);\n        rootElement.addEventListener(\"beforeinput\", handleBeforeInput);\n        rootElement.addEventListener(\"input\", handleInput);\n        // Cleanup\n        return function() {\n            rootElement.removeEventListener(\"compositionstart\", handleCompositionStart);\n            rootElement.removeEventListener(\"compositionupdate\", handleCompositionUpdate);\n            rootElement.removeEventListener(\"compositionend\", handleCompositionEnd);\n            rootElement.removeEventListener(\"beforeinput\", handleBeforeInput);\n            rootElement.removeEventListener(\"input\", handleInput);\n        };\n    }, [\n        editor,\n        enabled,\n        debug\n    ]);\n    return null;\n}\n_s(IMEPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n    return [\n        _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_3__.useLexicalComposerContext\n    ];\n});\n_c = IMEPlugin;\nvar _c;\n$RefreshReg$(_c, \"IMEPlugin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/plugins/ime-plugin.tsx\n"));

/***/ }),

/***/ "./libs/web/utils/ime-state-manager.ts":
/*!*********************************************!*\
  !*** ./libs/web/utils/ime-state-manager.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"IMEStateManager\": function() { return /* binding */ IMEStateManager; },\n/* harmony export */   \"createSmartOnChange\": function() { return /* binding */ createSmartOnChange; },\n/* harmony export */   \"getGlobalIMEStateManager\": function() { return /* binding */ getGlobalIMEStateManager; },\n/* harmony export */   \"isCurrentlyComposing\": function() { return /* binding */ isCurrentlyComposing; },\n/* harmony export */   \"shouldPauseExpensiveOperations\": function() { return /* binding */ shouldPauseExpensiveOperations; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_class_call_check_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/src/_class_call_check.mjs */ \"./node_modules/@swc/helpers/src/_class_call_check.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/**\n * 简化的IME状态管理器\n * 基于ProseMirror最佳实践，采用最小干预原则\n *\n * 设计理念：\n * 1. 信任ProseMirror - 让ProseMirror处理大部分IME逻辑\n * 2. 最小干预 - 只跟踪必要的composition状态\n * 3. 避免冲突 - 不与ProseMirror内置处理产生冲突\n * 4. 简单可靠 - 移除复杂的定时器和多层事件处理\n */ \n\n\n\nvar IMEStateManager = /*#__PURE__*/ function() {\n    \"use strict\";\n    function IMEStateManager() {\n        var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        (0,_swc_helpers_src_class_call_check_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, IMEStateManager);\n        this.state = {\n            isComposing: false,\n            lastCompositionData: null,\n            lastEventTime: 0,\n            compositionKey: null,\n            compositionRange: null,\n            environment:  false ? 0 : \"development\",\n            protectionEnabled: \"development\" === \"production\",\n            lastValidState: null,\n            anomalyCount: 0\n        };\n        this.listeners = new Set();\n        this.debug = options.debug || false;\n        if (this.debug) {\n            console.log(\"\\uD83C\\uDFAF IMEStateManager: Initialized with minimal intervention approach\");\n        }\n    }\n    var _proto = IMEStateManager.prototype;\n    /**\n     * 手动更新composition状态\n     * 借鉴Lexical：支持更细粒度的状态跟踪\n     */ _proto.updateCompositionState = function updateCompositionState(isComposing, data, options) {\n        var oldState = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, this.state);\n        var now = Date.now();\n        // 生成composition key（借鉴Lexical的做法）\n        var compositionKey = isComposing ? (options === null || options === void 0 ? void 0 : options.key) || \"comp_\".concat(now, \"_\").concat(Math.random().toString(36).substring(2, 11)) : null;\n        this.state = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, this.state), {\n            isComposing: isComposing,\n            lastCompositionData: data || null,\n            lastEventTime: now,\n            compositionKey: compositionKey,\n            compositionRange: (options === null || options === void 0 ? void 0 : options.range) || null\n        });\n        // 检测异常情况（借鉴Lexical的主动检测）\n        this.detectAnomalies(oldState);\n        // 只在状态真正变化时通知监听器\n        var shouldNotify = (options === null || options === void 0 ? void 0 : options.forceUpdate) || oldState.isComposing !== this.state.isComposing || oldState.lastCompositionData !== this.state.lastCompositionData;\n        if (shouldNotify) {\n            this.notifyListeners();\n            if (this.debug) {\n                console.log(\"\\uD83C\\uDFAF IMEStateManager: Composition state updated\", {\n                    isComposing: isComposing,\n                    data: data,\n                    compositionKey: compositionKey,\n                    range: options === null || options === void 0 ? void 0 : options.range,\n                    timestamp: now,\n                    environment: this.state.environment,\n                    anomalyCount: this.state.anomalyCount\n                });\n            }\n        }\n    };\n    _proto.notifyListeners = function notifyListeners() {\n        var _this = this;\n        this.listeners.forEach(function(listener) {\n            try {\n                listener(_this.state);\n            } catch (error) {\n                console.error(\"\\uD83C\\uDFAF IMEStateManager: Listener error\", error);\n            }\n        });\n    };\n    /**\n     * 检测异常情况（借鉴Lexical的主动检测机制）\n     */ _proto.detectAnomalies = function detectAnomalies(oldState) {\n        var now = Date.now();\n        var timeSinceLastEvent = now - oldState.lastEventTime;\n        // 检测1: 异常快速的事件序列（可能表示事件冲突）\n        if (timeSinceLastEvent < 10 && oldState.lastEventTime > 0) {\n            this.state.anomalyCount++;\n            if (this.debug) {\n                console.warn(\"\\uD83D\\uDEA8 IMEStateManager: Rapid event sequence detected\", {\n                    timeSinceLastEvent: timeSinceLastEvent,\n                    currentData: this.state.lastCompositionData,\n                    previousData: oldState.lastCompositionData,\n                    anomalyCount: this.state.anomalyCount\n                });\n            }\n        }\n        // 检测2: composition被意外中断\n        if (this.state.isComposing && oldState.isComposing && this.state.compositionKey !== oldState.compositionKey) {\n            this.state.anomalyCount++;\n            if (this.debug) {\n                console.warn(\"\\uD83D\\uDEA8 IMEStateManager: Composition interrupted\", {\n                    previousKey: oldState.compositionKey,\n                    newKey: this.state.compositionKey,\n                    previousData: oldState.lastCompositionData,\n                    newData: this.state.lastCompositionData\n                });\n            }\n        }\n        // 检测3: 生产环境特殊检测\n        if (this.state.environment === \"production\" && this.state.protectionEnabled) {\n            // 检测空的composition结束（常见的生产环境问题）\n            if (!this.state.isComposing && oldState.isComposing && (!this.state.lastCompositionData || this.state.lastCompositionData.trim() === \"\") && oldState.lastCompositionData && oldState.lastCompositionData.trim() !== \"\") {\n                this.state.anomalyCount++;\n                if (this.debug) {\n                    console.warn(\"\\uD83D\\uDEA8 IMEStateManager: Empty composition end in production\", {\n                        previousData: oldState.lastCompositionData,\n                        environment: this.state.environment\n                    });\n                }\n            }\n        }\n        // 重置异常计数（如果长时间没有异常）\n        if (timeSinceLastEvent > 5000) {\n            this.state.anomalyCount = Math.max(0, this.state.anomalyCount - 1);\n        }\n    };\n    /**\n     * 订阅状态变化\n     */ _proto.subscribe = function subscribe(listener) {\n        var _this = this;\n        this.listeners.add(listener);\n        // 返回取消订阅函数\n        return function() {\n            _this.listeners.delete(listener);\n        };\n    };\n    /**\n     * 获取当前状态\n     */ _proto.getState = function getState() {\n        return (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, this.state);\n    };\n    /**\n     * 检查是否正在组合输入\n     */ _proto.isComposing = function isComposing() {\n        return this.state.isComposing;\n    };\n    /**\n     * 检查是否应该暂停昂贵操作\n     * 简化逻辑：只在composition期间暂停\n     */ _proto.shouldPauseExpensiveOperations = function shouldPauseExpensiveOperations() {\n        return this.state.isComposing;\n    };\n    /**\n     * 手动设置状态（用于测试和调试）\n     */ _proto.setState = function setState(updates) {\n        var oldState = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, this.state);\n        this.state = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, this.state, updates);\n        if (oldState.isComposing !== this.state.isComposing) {\n            this.notifyListeners();\n        }\n    };\n    /**\n     * 销毁状态管理器\n     * 简化版本：只清理监听器\n     */ _proto.destroy = function destroy() {\n        this.listeners.clear();\n        if (this.debug) {\n            console.log(\"\\uD83C\\uDFAF IMEStateManager: Destroyed\");\n        }\n    };\n    return IMEStateManager;\n}();\n// 全局实例\nvar globalIMEStateManager = null;\n/**\n * 获取全局IME状态管理器\n */ function getGlobalIMEStateManager() {\n    if (!globalIMEStateManager) {\n        globalIMEStateManager = new IMEStateManager({\n            debug: \"development\" === \"development\"\n        });\n    }\n    return globalIMEStateManager;\n}\n/**\n * 检查当前是否正在进行IME输入\n */ function isCurrentlyComposing() {\n    return getGlobalIMEStateManager().isComposing();\n}\n/**\n * 检查是否应该暂停昂贵操作\n */ function shouldPauseExpensiveOperations() {\n    return getGlobalIMEStateManager().shouldPauseExpensiveOperations();\n}\n/**\n * 创建智能的onChange包装器\n * 在IME输入期间暂停昂贵操作，确保中文输入不被打断\n * 简化版本：只在composition期间延迟执行\n */ function createSmartOnChange(originalCallback) {\n    var options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _delay = options.delay, delay = _delay === void 0 ? 200 : _delay, _debug = options.debug, debug = _debug === void 0 ? false : _debug;\n    var stateManager = getGlobalIMEStateManager();\n    var pendingCall = null;\n    var timeoutId = null;\n    var executeCallback = function(args) {\n        if (debug) {\n            console.log(\"\\uD83C\\uDFAF SmartOnChange: Executing callback\");\n        }\n        return originalCallback.apply(void 0, (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(args));\n    };\n    var smartCallback = function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        var state = stateManager.getState();\n        if (debug) {\n            console.log(\"\\uD83C\\uDFAF SmartOnChange: Called\", {\n                isComposing: state.isComposing,\n                shouldPause: stateManager.shouldPauseExpensiveOperations()\n            });\n        }\n        // 如果正在IME输入，延迟执行\n        if (stateManager.shouldPauseExpensiveOperations()) {\n            pendingCall = {\n                args: args,\n                timestamp: Date.now()\n            };\n            if (timeoutId) {\n                clearTimeout(timeoutId);\n            }\n            timeoutId = setTimeout(function() {\n                if (pendingCall && !stateManager.shouldPauseExpensiveOperations()) {\n                    executeCallback(pendingCall.args);\n                    pendingCall = null;\n                }\n            }, delay);\n        } else {\n            // 立即执行\n            if (timeoutId) {\n                clearTimeout(timeoutId);\n                timeoutId = null;\n            }\n            executeCallback(args);\n        }\n    };\n    return smartCallback;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/utils/ime-state-manager.ts\n"));

/***/ })

});