"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/lexical-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/lexical-editor.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"./node_modules/@lexical/react/LexicalComposer.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"./node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"./node_modules/@lexical/react/LexicalContentEditable.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"./node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"./node_modules/@lexical/react/LexicalAutoFocusPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"./node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @lexical/react/LexicalLinkPlugin */ \"./node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @lexical/react/LexicalListPlugin */ \"./node_modules/@lexical/react/LexicalListPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @lexical/react/LexicalMarkdownShortcutPlugin */ \"./node_modules/@lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs\");\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @lexical/markdown */ \"./node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"./node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/rich-text */ \"./node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/code */ \"./node_modules/@lexical/code/LexicalCode.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-div-100vh */ \"./node_modules/react-div-100vh/dist/esm/index.js\");\n/* harmony import */ var libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/hooks/use-mounted */ \"./libs/web/hooks/use-mounted.ts\");\n/* harmony import */ var libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/hooks/use-i18n */ \"./libs/web/hooks/use-i18n.tsx\");\n/* harmony import */ var _plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plugins/slash-commands-plugin */ \"./components/editor/plugins/slash-commands-plugin.tsx\");\n/* harmony import */ var _plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./plugins/floating-toolbar-plugin */ \"./components/editor/plugins/floating-toolbar-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @lexical/react/LexicalCheckListPlugin */ \"./node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs\");\n/* harmony import */ var _plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./plugins/highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var _plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./plugins/image-plugin */ \"./components/editor/plugins/image-plugin.tsx\");\n/* harmony import */ var _plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./plugins/ime-plugin */ \"./components/editor/plugins/ime-plugin.tsx\");\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"./node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRulePlugin */ \"./node_modules/@lexical/react/LexicalHorizontalRulePlugin.dev.mjs\");\n/**\n * Lexical Editor Component\n * Migrated from TipTap to Lexical for better performance and modern architecture\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import custom plugins and nodes\n\n\n\n\n\n\n\n\n\nvar theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\",\n    quote: \"editor-quote\",\n    heading: {\n        h1: \"editor-heading-h1\",\n        h2: \"editor-heading-h2\",\n        h3: \"editor-heading-h3\",\n        h4: \"editor-heading-h4\",\n        h5: \"editor-heading-h5\",\n        h6: \"editor-heading-h6\"\n    },\n    list: {\n        nested: {\n            listitem: \"editor-nested-listitem\"\n        },\n        ol: \"editor-list-ol\",\n        ul: \"editor-list-ul\",\n        listitem: \"editor-listitem\",\n        // 按深度定义不同的列表样式 - 这是关键！\n        olDepth: [\n            \"editor-list-ol-1\",\n            \"editor-list-ol-2\",\n            \"editor-list-ol-3\",\n            \"editor-list-ol-4\",\n            \"editor-list-ol-5\" // I, II, III...\n        ],\n        ulDepth: [\n            \"editor-list-ul-1\",\n            \"editor-list-ul-2\",\n            \"editor-list-ul-3\",\n            \"editor-list-ul-4\",\n            \"editor-list-ul-5\" // ‣\n        ],\n        checklist: \"PlaygroundEditorTheme__checklist\",\n        listitemChecked: \"PlaygroundEditorTheme__listItemChecked\",\n        listitemUnchecked: \"PlaygroundEditorTheme__listItemUnchecked\"\n    },\n    // Lexical 0.32.1 原生缩进支持\n    indent: \"lexical-indent\",\n    image: \"editor-image\",\n    link: \"editor-link\",\n    text: {\n        bold: \"editor-text-bold\",\n        italic: \"editor-text-italic\",\n        overflowed: \"editor-text-overflowed\",\n        hashtag: \"editor-text-hashtag\",\n        underline: \"editor-text-underline\",\n        strikethrough: \"editor-text-strikethrough\",\n        underlineStrikethrough: \"editor-text-underlineStrikethrough\",\n        code: \"editor-text-code\",\n        highlight: \"editor-text-highlight\"\n    },\n    code: \"editor-code\",\n    codeHighlight: {\n        atrule: \"editor-tokenAttr\",\n        attr: \"editor-tokenAttr\",\n        boolean: \"editor-tokenProperty\",\n        builtin: \"editor-tokenSelector\",\n        cdata: \"editor-tokenComment\",\n        char: \"editor-tokenSelector\",\n        class: \"editor-tokenFunction\",\n        \"class-name\": \"editor-tokenFunction\",\n        comment: \"editor-tokenComment\",\n        constant: \"editor-tokenProperty\",\n        deleted: \"editor-tokenProperty\",\n        doctype: \"editor-tokenComment\",\n        entity: \"editor-tokenOperator\",\n        function: \"editor-tokenFunction\",\n        important: \"editor-tokenVariable\",\n        inserted: \"editor-tokenSelector\",\n        keyword: \"editor-tokenAttr\",\n        namespace: \"editor-tokenVariable\",\n        number: \"editor-tokenProperty\",\n        operator: \"editor-tokenOperator\",\n        prolog: \"editor-tokenComment\",\n        property: \"editor-tokenProperty\",\n        punctuation: \"editor-tokenPunctuation\",\n        regex: \"editor-tokenVariable\",\n        selector: \"editor-tokenSelector\",\n        string: \"editor-tokenSelector\",\n        symbol: \"editor-tokenProperty\",\n        tag: \"editor-tokenProperty\",\n        url: \"editor-tokenOperator\",\n        variable: \"editor-tokenVariable\"\n    }\n};\nfunction Placeholder() {\n    _s();\n    var t = (0,libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"])().t;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: t(\"Start writing...\")\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 153,\n        columnNumber: 12\n    }, this);\n}\n_s(Placeholder, \"82N5KF9nLzZ6+2WH7KIjzIXRkLw=\", false, function() {\n    return [\n        libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Placeholder;\nvar LexicalEditor = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(_c1 = _s1(function(param, ref) {\n    var _readOnly = param.readOnly, readOnly = _readOnly === void 0 ? false : _readOnly, _value = param.value, value = _value === void 0 ? \"\" : _value, onChange = param.onChange, onClickLink = param.onClickLink, onHoverLink = param.onHoverLink, _className = param.className, className = _className === void 0 ? \"\" : _className;\n    _s1();\n    var _s = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n    var height = (0,react_div_100vh__WEBPACK_IMPORTED_MODULE_4__.use100vh)();\n    var mounted = (0,libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // 创建一个 ref 来存储编辑器实例\n    var editorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    var initialConfig = {\n        namespace: \"LexicalEditor\",\n        theme: theme,\n        onError: function onError(error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n            // Lexical原生的ListItemNode已经支持checkbox功能\n            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n        ],\n        editable: !readOnly,\n        // 设置初始内容\n        editorState: value ? undefined : null\n    };\n    // 创建自定义transformers，包含图片支持\n    var IMAGE_TRANSFORMER = {\n        dependencies: [\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode\n        ],\n        export: function(node) {\n            if (!(0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$isImageNode)(node)) {\n                return null;\n            }\n            return \"![\".concat(node.getAltText(), \"](\").concat(node.getSrc(), \")\");\n        },\n        regExp: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/,\n        replace: function(parentNode, children, match) {\n            var _match = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(match, 3), altText = _match[1], src = _match[2];\n            var imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$createImageNode)({\n                altText: altText,\n                src: src,\n                maxWidth: 800\n            });\n            children.forEach(function(child) {\n                return child.remove();\n            });\n            parentNode.append(imageNode);\n        },\n        type: \"element\"\n    };\n    // 创建自定义的下划线转换器，使用 <u>text</u> 语法\n    var UNDERLINE_TRANSFORMER = {\n        format: [\n            \"underline\"\n        ],\n        tag: \"<u>\",\n        type: \"text-format\"\n    };\n    // 移除段落缩进转换器 - 专注于列表缩进功能\n    // 段落缩进不是标准markdown语法，我们只处理列表缩进\n    // 创建水平分割线转换器\n    var HR_TRANSFORMER = {\n        dependencies: [\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode\n        ],\n        export: function(node) {\n            return (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$isHorizontalRuleNode)(node) ? \"---\" : null;\n        },\n        regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n        replace: function(parentNode, children, match, isImport) {\n            var line = (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$createHorizontalRuleNode)();\n            if (isImport || parentNode.getNextSibling() != null) {\n                parentNode.replace(line);\n            } else {\n                parentNode.insertBefore(line);\n            }\n            line.selectNext();\n        },\n        type: \"element\"\n    };\n    // 🔧 JSON格式保存 - 完全使用Lexical原生格式\n    // 使用标准的Lexical transformers\n    var customTransformers = (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(_lexical_markdown__WEBPACK_IMPORTED_MODULE_19__.TRANSFORMERS).concat([\n        // 添加我们的自定义转换器\n        HR_TRANSFORMER,\n        UNDERLINE_TRANSFORMER,\n        IMAGE_TRANSFORMER\n    ]);\n    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题\n    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(editorState, _editor, tags) {\n        if (onChange) {\n            // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新\n            if (tags.has(\"history-merge\") || tags.has(\"content-sync\")) {\n                return;\n            }\n            editorState.read(function() {\n                try {\n                    // 使用Lexical原生JSON格式保存，完美保持嵌套关系\n                    var editorStateJSON = JSON.stringify(editorState.toJSON());\n                    // 简单的内容变化检查（比较JSON字符串）\n                    if (editorStateJSON !== value) {\n                        onChange(function() {\n                            return editorStateJSON;\n                        });\n                    }\n                } catch (error) {\n                    console.error(\"Error in handleChange:\", error);\n                // 如果转换出错，保持原有内容不变\n                }\n            });\n        }\n    }, [\n        onChange,\n        value\n    ]);\n    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑\n    var ListExitPlugin = function() {\n        _s();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_20__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            return editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_21__.KEY_ENTER_COMMAND, function(event) {\n                var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$getSelection)();\n                if (!(0,lexical__WEBPACK_IMPORTED_MODULE_21__.$isRangeSelection)(selection)) {\n                    return false;\n                }\n                var anchorNode = selection.anchor.getNode();\n                // 检查是否在空的列表项中\n                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(anchorNode)) {\n                    var textContent = anchorNode.getTextContent().trim();\n                    if (textContent === \"\") {\n                        var listNode = anchorNode.getParent();\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(listNode)) {\n                            // 如果是空的列表项，退出列表\n                            event === null || event === void 0 ? void 0 : event.preventDefault();\n                            // 创建新段落并在列表后插入\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$createParagraphNode)();\n                            listNode.insertAfter(paragraph);\n                            // 删除空的列表项\n                            anchorNode.remove();\n                            // 选中新段落\n                            paragraph.select();\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            }, lexical__WEBPACK_IMPORTED_MODULE_21__.COMMAND_PRIORITY_HIGH);\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s(ListExitPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_20__.useLexicalComposerContext\n        ];\n    });\n    // 内容同步组件 - 模仿TipTap的方式\n    var ContentSyncPlugin = function() {\n        _s2();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_20__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (editor && value !== undefined && mounted) {\n                // 使用setTimeout来避免在渲染过程中调用flushSync\n                setTimeout(function() {\n                    if (value.trim()) {\n                        try {\n                            // 解析JSON格式的编辑器状态\n                            var editorStateData = JSON.parse(value);\n                            // 直接设置编辑器状态\n                            var newEditorState = editor.parseEditorState(editorStateData);\n                            editor.setEditorState(newEditorState);\n                        } catch (jsonError) {\n                            // JSON解析失败，创建空编辑器\n                            editor.update(function() {\n                                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$getRoot)();\n                                root.clear();\n                                var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$createParagraphNode)();\n                                root.append(paragraph);\n                            });\n                        }\n                    } else {\n                        // 空内容时清空并创建一个空段落\n                        editor.update(function() {\n                            var root = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$getRoot)();\n                            root.clear();\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$createParagraphNode)();\n                            root.append(paragraph);\n                        });\n                    }\n                }, 0);\n            }\n        }, [\n            editor,\n            value,\n            mounted\n        ]);\n        return null;\n    };\n    _s2(ContentSyncPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_20__.useLexicalComposerContext\n        ];\n    });\n    // 编辑器引用组件 - 获取编辑器实例\n    var EditorRefPlugin = function() {\n        _s3();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_20__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            editorRef.current = editor;\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s3(EditorRefPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_20__.useLexicalComposerContext\n        ];\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function() {\n        return {\n            focusAtEnd: function() {\n                // 实现焦点到编辑器末尾\n                if (editorRef.current) {\n                    editorRef.current.focus(function() {\n                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$getRoot)();\n                        var lastChild = root.getLastChild();\n                        if (lastChild) {\n                            lastChild.selectEnd();\n                        } else {\n                            root.selectEnd();\n                        }\n                    });\n                }\n            },\n            focusAtStart: function() {\n                // 实现焦点到编辑器开始\n                if (editorRef.current) {\n                    editorRef.current.focus(function() {\n                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_21__.$getRoot)();\n                        var firstChild = root.getFirstChild();\n                        if (firstChild) {\n                            firstChild.selectStart();\n                        } else {\n                            root.selectStart();\n                        }\n                    });\n                }\n            }\n        };\n    });\n    if (!mounted) {\n        return null;\n    }\n    // 调试信息\n    console.log(\"\\uD83D\\uDD27 LexicalEditor 渲染:\", {\n        hasValue: !!value,\n        valueLength: (value === null || value === void 0 ? void 0 : value.length) || 0,\n        isValueJson: (value === null || value === void 0 ? void 0 : value.startsWith(\"{\")) || false,\n        hasOnChange: !!onChange,\n        readOnly: readOnly,\n        timestamp: new Date().toISOString()\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"b6e8ec94f89652f1\",\n                [\n                    height ? height + \"px\" : \"100vh\"\n                ]\n            ]\n        ]) + \" \" + \"lexical-editor \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__.LexicalComposer, {\n                initialConfig: initialConfig,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"b6e8ec94f89652f1\",\n                            [\n                                height ? height + \"px\" : \"100vh\"\n                            ]\n                        ]\n                    ]) + \" \" + \"editor-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__.ContentEditable, {\n                                className: \"editor-input focus:outline-none w-full\",\n                                spellCheck: false\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__.LinkPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__.ListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__.CheckListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__.MarkdownShortcutPlugin, {\n                            transformers: customTransformers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 455,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__.HorizontalRulePlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            enabled: true,\n                            debug: \"development\" === \"development\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListExitPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 460,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditorRefPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSyncPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__.OnChangePlugin, {\n                            onChange: handleChange,\n                            ignoreHistoryMergeTagChange: true,\n                            ignoreSelectionChange: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 21\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                lineNumber: 435,\n                columnNumber: 13\n            }, _this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"b6e8ec94f89652f1\",\n                dynamic: [\n                    height ? height + \"px\" : \"100vh\"\n                ],\n                children: \".lexical-editor{position:relative}.editor-container{position:relative}.editor-input{outline:none;padding:1rem 0;min-height:-webkit-calc(\".concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:-moz-calc(\").concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:calc(\").concat(height ? height + \"px\" : \"100vh\", ' - 14rem);padding-bottom:10rem;width:100%;max-width:none;line-height:1.7;font-size:1rem;color:inherit;-webkit-spellcheck:false;-moz-spellcheck:false;-ms-spellcheck:false;spellcheck:false}.lexical-editor{--lexical-indent-base-value:32px}.lexical-indent{--lexical-indent-base-value:32px}.editor-input p[style*=\"margin-left\"],.editor-input h1[style*=\"margin-left\"],.editor-input h2[style*=\"margin-left\"],.editor-input h3[style*=\"margin-left\"],.editor-input h4[style*=\"margin-left\"],.editor-input h5[style*=\"margin-left\"],.editor-input h6[style*=\"margin-left\"],.editor-input li[style*=\"margin-left\"]{-webkit-transition:margin-left.2s ease;-moz-transition:margin-left.2s ease;-o-transition:margin-left.2s ease;transition:margin-left.2s ease}.editor-placeholder{color:#999;overflow:hidden;position:absolute;-o-text-overflow:ellipsis;text-overflow:ellipsis;top:1rem;left:0;font-size:1rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;pointer-events:none}.editor-paragraph{margin:1.2rem 0;line-height:1.8}.editor-heading-h1{font-size:2.8em;font-weight:bold;margin:1.5rem 0 1rem 0;line-height:1.2}.editor-heading-h2{font-size:2.2em;font-weight:bold;margin:1.4rem 0 .8rem 0;line-height:1.3}.editor-heading-h3{font-size:1.8em;font-weight:bold;margin:1.3rem 0 .6rem 0;line-height:1.4}.editor-heading-h4{font-size:1.5em;font-weight:bold;margin:1.2rem 0 .5rem 0;line-height:1.4}.editor-heading-h5{font-size:1.3em;font-weight:bold;margin:1.1rem 0 .4rem 0;line-height:1.5}.editor-heading-h6{font-size:1.2em;font-weight:bold;margin:1rem 0 .3rem 0;line-height:1.5}.editor-quote{margin:1rem 0;padding-left:1rem;border-left:4px solid#ccc;font-style:italic;color:#666}.editor-list-ol,.editor-list-ul{margin:1rem 0;padding-left:2rem}.editor-listitem{margin:.5rem 0}.editor-link{color:#3b82f6;text-decoration:underline;cursor:pointer}.editor-link:hover{color:#1d4ed8}.editor-text-bold{font-weight:bold}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.editor-text-strikethrough{text-decoration:line-through}.editor-text-code{background-color:#e4e4e7;color:black;padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:monospace;font-size:.9em}.editor-code{background-color:#e4e4e7;color:black;border:1px solid#e9ecef;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;padding:1rem;margin:1rem 0;font-family:\"Courier New\",Courier,monospace;font-size:.9em;line-height:1.4;overflow-x:auto}[data-theme=\"dark\"] .editor-text-code{background-color:#3f3f46;color:white}[data-theme=\"dark\"] .editor-code{background-color:#3f3f46;color:white;border-color:#4b5563}.editor-list-ol{list-style-type:decimal;margin:.5rem 0;padding-left:1.5rem}.editor-list-ul{list-style-type:disc;margin:.5rem 0;padding-left:1.5rem}.editor-listitem{margin:.4rem 0;line-height:1.8}.editor-nested-listitem{margin:.3rem 0}.editor-list-ol-1{list-style-type:decimal}.editor-list-ol-2{list-style-type:lower-alpha}.editor-list-ol-3{list-style-type:lower-roman}.editor-list-ol-4{list-style-type:upper-alpha}.editor-list-ol-5{list-style-type:upper-roman}.editor-list-ul-1{list-style-type:disc}.editor-list-ul-2{list-style-type:circle}.editor-list-ul-3{list-style-type:square}.editor-list-ul-4{list-style-type:disc}.editor-list-ul-5{list-style-type:circle}.task-list{list-style:none;padding-left:0;margin:1rem 0}.task-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;margin:.5rem 0;list-style:none}.task-checkbox{margin-right:.5rem;margin-top:.125rem;cursor:pointer;width:1rem;height:1rem;border:1px solid#d1d5db;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;background:white;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-appearance:none}.task-checkbox:checked{background-color:#3b82f6;border-color:#3b82f6;color:white}.task-checkbox:checked::after{content:\"✓\";font-size:.75rem;font-weight:bold;color:white}.task-content{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:1.7}.task-item[data-checked=\"true\"] .task-content{text-decoration:line-through;opacity:.6}.lexical-highlight,mark,.editor-text-highlight{background-color:#eab834!important;color:black!important;padding:.1rem .2rem;-webkit-border-radius:.125rem;-moz-border-radius:.125rem;border-radius:.125rem}.dark .lexical-highlight,.dark mark,.dark .editor-text-highlight,[data-theme=\"dark\"] .lexical-highlight,[data-theme=\"dark\"] mark,[data-theme=\"dark\"] .editor-text-highlight,html.dark .lexical-highlight,html.dark mark,html.dark .editor-text-highlight{background-color:#3185eb!important;color:white!important}.editor-image img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}@media(prefers-color-scheme:dark){.editor-placeholder{color:#6b7280}.editor-quote{border-left-color:#4b5563;color:#9ca3af}.editor-text-code{background-color:#374151;color:#f9fafb}.editor-code{background-color:#1f2937;border-color:#374151;color:#f9fafb}}.lexical-editor ul:not([data-lexical-list-type=\"check\"]) li::marker{color:#6b7280}.lexical-editor ol li::marker{color:#6b7280}.lexical-editor .PlaygroundEditorTheme__listItemChecked,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked{position:relative;margin-left:.5em;margin-right:.5em;padding-left:1.5em;padding-right:1.5em;list-style-type:none;outline:none;display:block;min-height:1.5em}.lexical-editor .PlaygroundEditorTheme__listItemChecked>*,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked>*{margin-left:.01em}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{content:\"\";width:.9em;height:.9em;top:50%;left:0;cursor:pointer;display:block;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover;position:absolute;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.lexical-editor .PlaygroundEditorTheme__listItemChecked{text-decoration:line-through}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before{-webkit-box-shadow:0 0 0 2px#a6cdfe;-moz-box-shadow:0 0 0 2px#a6cdfe;box-shadow:0 0 0 2px#a6cdfe;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before{border:1px solid#999;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{border:1px solid rgb(61,135,245);-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;background-color:#3d87f5;background-repeat:no-repeat}.lexical-editor .PlaygroundEditorTheme__listItemChecked:after{content:\"\";cursor:pointer;border-color:#fff;border-style:solid;position:absolute;display:block;top:45%;width:.2em;left:.35em;height:.4em;-webkit-transform:translateY(-50%)rotate(45deg);-moz-transform:translateY(-50%)rotate(45deg);-ms-transform:translateY(-50%)rotate(45deg);-o-transform:translateY(-50%)rotate(45deg);transform:translateY(-50%)rotate(45deg);border-width:0 .1em .1em 0}')\n            }, void 0, false, void 0, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 434,\n        columnNumber: 9\n    }, _this);\n}, \"oS0wBeFl4MEW/6I6OJbEwOv9UcA=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n})), \"oS0wBeFl4MEW/6I6OJbEwOv9UcA=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c2 = LexicalEditor;\nLexicalEditor.displayName = \"LexicalEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Placeholder\");\n$RefreshReg$(_c1, \"LexicalEditor$forwardRef\");\n$RefreshReg$(_c2, \"LexicalEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-editor.tsx\n"));

/***/ })

});