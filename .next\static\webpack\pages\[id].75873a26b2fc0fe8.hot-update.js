"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/hooks/use-auto-save-on-leave.ts":
/*!**************************************************!*\
  !*** ./libs/web/hooks/use-auto-save-on-leave.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Auto Save on Leave Hook\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\n\n\nvar useAutoSaveOnLeave = function() {\n    var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _enabled = options.enabled, enabled = _enabled === void 0 ? true : _enabled;\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var isAutoSavingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    var shouldAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if ( true && window.saveButtonStatus) {\n            return window.saveButtonStatus === \"save\";\n        }\n        return false;\n    }, []);\n    var performAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function() {\n        var error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!( true && window.saveButtonAutoSave)) return [\n                        3,\n                        4\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        window.saveButtonAutoSave()\n                    ];\n                case 2:\n                    _state.sent();\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), []);\n    // 页面关闭/刷新处理 - 弹窗提示机制\n    var handleBeforeUnload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        if (!enabled) return;\n        if (shouldAutoSave()) {\n            // 阻止默认行为，显示确认对话框\n            event.preventDefault();\n            event.returnValue = \"您有未保存的更改。确定要离开吗？\";\n            // 使用延迟检测用户选择\n            setTimeout(function() {\n                // 如果能执行到这里，说明用户选择了\"取消\"，自动保存\n                performAutoSave();\n            }, 100);\n            return \"您有未保存的更改。确定要离开吗？\";\n        }\n    }, [\n        enabled,\n        shouldAutoSave,\n        performAutoSave\n    ]);\n    // 🔧 重构：笔记跳转处理 - 区分笔记ID变化\n    var handleRouteChangeStart = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function(url) {\n            var isNoteNavigation, success, confirmed, error, confirmed1, confirmed2;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!enabled || isAutoSavingRef.current) return [\n                            2\n                        ];\n                        if (!shouldAutoSave()) return [\n                            3,\n                            8\n                        ];\n                        console.log(\"\\uD83D\\uDD27 检测到路由跳转，有未保存内容\", {\n                            targetUrl: url\n                        });\n                        isNoteNavigation = url.match(/^\\/[a-zA-Z0-9-]+$/) || url === \"/\";\n                        if (!isNoteNavigation) return [\n                            3,\n                            5\n                        ];\n                        // 笔记跳转：直接自动保存\n                        isAutoSavingRef.current = true;\n                        // 阻止路由跳转\n                        router.events.emit(\"routeChangeError\", new Error(\"Auto-saving before route change\"), url);\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            performAutoSave()\n                        ];\n                    case 2:\n                        success = _state.sent();\n                        isAutoSavingRef.current = false;\n                        if (success) {\n                            router.push(url);\n                        } else {\n                            confirmed = window.confirm(\"自动保存失败。是否强制离开？\");\n                            if (confirmed) {\n                                router.push(url);\n                            }\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"自动保存出错:\", error);\n                        isAutoSavingRef.current = false;\n                        confirmed1 = window.confirm(\"自动保存出错。是否强制离开？\");\n                        if (confirmed1) {\n                            router.push(url);\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            3,\n                            8\n                        ];\n                    case 5:\n                        // 非笔记跳转：弹窗提示\n                        router.events.emit(\"routeChangeError\", new Error(\"User confirmation required\"), url);\n                        confirmed2 = window.confirm(\"您有未保存的更改。确定要离开吗？\");\n                        if (!confirmed2) return [\n                            3,\n                            6\n                        ];\n                        router.push(url);\n                        return [\n                            3,\n                            8\n                        ];\n                    case 6:\n                        return [\n                            4,\n                            performAutoSave()\n                        ];\n                    case 7:\n                        _state.sent();\n                        _state.label = 8;\n                    case 8:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(url) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        enabled,\n        shouldAutoSave,\n        performAutoSave,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return function() {\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        enabled,\n        handleBeforeUnload\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        router.events.on(\"routeChangeStart\", handleRouteChangeStart);\n        return function() {\n            router.events.off(\"routeChangeStart\", handleRouteChangeStart);\n        };\n    }, [\n        enabled,\n        handleRouteChangeStart,\n        router.events\n    ]);\n    return {\n        shouldAutoSave: shouldAutoSave,\n        performAutoSave: performAutoSave\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useAutoSaveOnLeave);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/hooks/use-auto-save-on-leave.ts\n"));

/***/ })

});