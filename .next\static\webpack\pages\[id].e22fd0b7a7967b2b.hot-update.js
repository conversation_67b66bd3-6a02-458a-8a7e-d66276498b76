"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/state/lexical-editor.ts":
/*!******************************************!*\
  !*** ./libs/web/state/lexical-editor.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\n\n\n\n\nvar ROOT_ID = \"root\";\nvar useLexicalEditor = function(initNote) {\n    // Use initNote if provided, otherwise try to get from NoteState\n    var note = initNote;\n    var createNoteWithTitle, updateNote, createNote;\n    try {\n        var noteState = libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__[\"default\"].useContainer();\n        createNoteWithTitle = noteState.createNoteWithTitle;\n        updateNote = noteState.updateNote;\n        createNote = noteState.createNote;\n        // Only use noteState.note if no initNote is provided\n        if (!note) {\n            note = noteState.note;\n        }\n    } catch (error) {\n        // If NoteState is not available, we'll work with just the initNote\n        console.warn(\"NoteState not available in LexicalEditorState, using initNote only\");\n        createNoteWithTitle = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        updateNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        createNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n    }\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    var editorEl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 🔧 新增：快照状态管理\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), noteSnapshot = ref[0], setNoteSnapshot = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), currentEditorContent = ref1[0], setCurrentEditorContent = ref1[1];\n    // Manual save function for IndexedDB\n    var saveToIndexedDB = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(data) {\n            var existingNote, baseNote, updatedNote;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            2\n                        ];\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        existingNote = _state.sent();\n                        baseNote = existingNote || note;\n                        updatedNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, baseNote, data);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(note.id, updatedNote)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        note\n    ]);\n    var syncToServer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var isNew, localNote, noteToSave, noteData, item, noteUrl, updatedNote, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_6__.has)(router.query, \"new\");\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        11,\n                        ,\n                        12\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    localNote = _state.sent();\n                    noteToSave = localNote || note;\n                    if (!isNew) return [\n                        3,\n                        7\n                    ];\n                    noteData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, noteToSave), {\n                        pid: router.query.pid || ROOT_ID\n                    });\n                    return [\n                        4,\n                        createNote(noteData)\n                    ];\n                case 3:\n                    item = _state.sent();\n                    if (!item) return [\n                        3,\n                        6\n                    ];\n                    noteUrl = \"/\".concat(item.id);\n                    if (!(router.asPath !== noteUrl)) return [\n                        3,\n                        5\n                    ];\n                    return [\n                        4,\n                        router.replace(noteUrl, undefined, {\n                            shallow: true\n                        })\n                    ];\n                case 4:\n                    _state.sent();\n                    _state.label = 5;\n                case 5:\n                    toast(\"Note saved to server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 6:\n                    return [\n                        3,\n                        10\n                    ];\n                case 7:\n                    return [\n                        4,\n                        updateNote(noteToSave)\n                    ];\n                case 8:\n                    updatedNote = _state.sent();\n                    if (!updatedNote) return [\n                        3,\n                        10\n                    ];\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(updatedNote.id, updatedNote)\n                    ];\n                case 9:\n                    _state.sent();\n                    toast(\"Note updated on server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 10:\n                    return [\n                        3,\n                        12\n                    ];\n                case 11:\n                    error = _state.sent();\n                    toast(\"Failed to save note to server\", \"error\");\n                    return [\n                        2,\n                        false\n                    ];\n                case 12:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), [\n        note,\n        router,\n        createNote,\n        updateNote,\n        toast\n    ]);\n    var onCreateLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(title) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!createNoteWithTitle) return [\n                            2,\n                            \"\"\n                        ];\n                        return [\n                            4,\n                            createNoteWithTitle(title)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (result === null || result === void 0 ? void 0 : result.id) {\n                            return [\n                                2,\n                                \"/\".concat(result.id)\n                            ];\n                        }\n                        return [\n                            2,\n                            \"\"\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNoteWithTitle\n    ]);\n    var onSearchLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(term) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    []\n                ];\n            });\n        });\n        return function(term) {\n            return _ref.apply(this, arguments);\n        };\n    }(), []);\n    var onClickLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(href, event) {\n        if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_3__.isNoteLink)(href)) {\n            event.preventDefault();\n            router.push(href);\n        } else {\n            window.open(href, \"_blank\", \"noopener,noreferrer\");\n        }\n    }, [\n        router\n    ]);\n    var onUploadImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(_file, _id) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                // Image upload is disabled in PostgreSQL version\n                toast(\"Image upload is not supported in this version\", \"error\");\n                throw new Error(\"Image upload is not supported\");\n            });\n        });\n        return function(_file, _id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        toast\n    ]);\n    var onHoverLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(event) {\n        return true;\n    }, []);\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), backlinks = ref2[0], setBackLinks = ref2[1];\n    var getBackLinks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var linkNotes;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(note === null || note === void 0 ? void 0 : note.id);\n                    linkNotes = [];\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        linkNotes\n                    ];\n                    setBackLinks([]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].iterate(function(value) {\n                            var ref;\n                            if ((ref = value.linkIds) === null || ref === void 0 ? void 0 : ref.includes((note === null || note === void 0 ? void 0 : note.id) || \"\")) {\n                                linkNotes.push(value);\n                            }\n                        })\n                    ];\n                case 1:\n                    _state.sent();\n                    setBackLinks(linkNotes);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 🔧 快照初始化逻辑 - 打开笔记时设置JSON快照\n    var initializeSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var cachedNote, snapshotJsonContent, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(\"\\uD83D\\uDD27 开始初始化快照:\", {\n                        noteId: note === null || note === void 0 ? void 0 : note.id\n                    });\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) {\n                        // 新建笔记：快照为空值\n                        console.log(\"\\uD83D\\uDD27 新建笔记，设置空快照\");\n                        setNoteSnapshot(null);\n                        setCurrentEditorContent(\"\");\n                        return [\n                            2\n                        ];\n                    }\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    cachedNote = _state.sent();\n                    snapshotJsonContent = (cachedNote === null || cachedNote === void 0 ? void 0 : cachedNote.content) || \"\";\n                    // 如果缓存为空，尝试从 note 对象获取内容\n                    if (!snapshotJsonContent && note.content) {\n                        snapshotJsonContent = note.content;\n                        console.log(\"\\uD83D\\uDD27 缓存为空，使用 note 对象内容\");\n                    }\n                    console.log(\"\\uD83D\\uDD27 已存在笔记，设置快照:\", {\n                        noteId: note.id,\n                        hasContent: !!snapshotJsonContent,\n                        contentLength: snapshotJsonContent.length,\n                        isJson: snapshotJsonContent.startsWith(\"{\"),\n                        source: (cachedNote === null || cachedNote === void 0 ? void 0 : cachedNote.content) ? \"cache\" : \"note\"\n                    });\n                    setNoteSnapshot(snapshotJsonContent);\n                    setCurrentEditorContent(snapshotJsonContent);\n                    return [\n                        3,\n                        4\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"JSON快照初始化失败:\", error);\n                    // 失败时设置为空快照\n                    setNoteSnapshot(null);\n                    setCurrentEditorContent(\"\");\n                    return [\n                        3,\n                        4\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 当笔记ID变化时初始化快照\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        initializeSnapshot();\n    }, [\n        initializeSnapshot\n    ]);\n    // 简化的 onChange 处理 - 只更新当前编辑器内容，不做其他操作\n    var onEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(getValue) {\n        var jsonContent = getValue();\n        // 只更新当前编辑器内容状态，其他逻辑交给 SaveButton 处理\n        setCurrentEditorContent(jsonContent);\n    }, []);\n    // Function to handle title changes specifically\n    var onTitleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(title) {\n        var ref;\n        (ref = saveToIndexedDB({\n            title: title,\n            updated_at: new Date().toISOString()\n        })) === null || ref === void 0 ? void 0 : ref.catch(function(v) {\n            return console.error(\"Error whilst saving title to IndexedDB: %O\", v);\n        });\n    }, [\n        saveToIndexedDB\n    ]);\n    // 🔧 修复：JSON快照对比功能 - 供SaveButton使用\n    var compareWithSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        // 如果是新建笔记（快照为null），任何JSON内容都算作变化\n        if (noteSnapshot === null) {\n            return currentEditorContent.trim() !== \"\";\n        }\n        // 已存在笔记：比较当前JSON内容与JSON快照\n        var hasChanges = currentEditorContent !== noteSnapshot;\n        return hasChanges;\n    }, [\n        noteSnapshot,\n        currentEditorContent\n    ]);\n    // 🔧 新增：获取当前编辑器状态 - 供SaveButton使用\n    var getEditorState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        return {\n            hasChanges: compareWithSnapshot(),\n            currentContent: currentEditorContent,\n            snapshot: noteSnapshot,\n            isNewNote: noteSnapshot === null\n        };\n    }, [\n        compareWithSnapshot,\n        currentEditorContent,\n        noteSnapshot\n    ]);\n    // 🔧 修复：保存当前JSON内容到IndexedDB - 供SaveButton调用\n    var saveCurrentContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var title, titleInput, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    if (note === null || note === void 0 ? void 0 : note.isDailyNote) {\n                        title = note.title;\n                    } else {\n                        titleInput = document.querySelector(\"h1 textarea\");\n                        if (titleInput && titleInput.value) {\n                            title = titleInput.value.trim();\n                        } else {\n                            // 对于JSON格式，使用现有标题或默认标题\n                            title = (note === null || note === void 0 ? void 0 : note.title) || \"Untitled\";\n                        }\n                    }\n                    // 保存JSON内容到IndexedDB\n                    return [\n                        4,\n                        saveToIndexedDB({\n                            content: currentEditorContent,\n                            title: title,\n                            updated_at: new Date().toISOString()\n                        })\n                    ];\n                case 2:\n                    _state.sent();\n                    // 🔧 关键修复：保存成功后更新快照，使状态变为一致\n                    setNoteSnapshot(currentEditorContent);\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 保存JSON到IndexedDB失败:\", error);\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note,\n        currentEditorContent,\n        saveToIndexedDB\n    ]);\n    return {\n        onCreateLink: onCreateLink,\n        onSearchLink: onSearchLink,\n        onClickLink: onClickLink,\n        onUploadImage: onUploadImage,\n        onHoverLink: onHoverLink,\n        getBackLinks: getBackLinks,\n        onEditorChange: onEditorChange,\n        onTitleChange: onTitleChange,\n        saveToIndexedDB: saveToIndexedDB,\n        syncToServer: syncToServer,\n        backlinks: backlinks,\n        editorEl: editorEl,\n        note: note,\n        // 🔧 新增：快照对比相关功能\n        getEditorState: getEditorState,\n        saveCurrentContent: saveCurrentContent,\n        compareWithSnapshot: compareWithSnapshot,\n        // 🔧 关键修复：返回当前编辑器内容作为编辑器的 value\n        editorNote: (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, note), {\n            content: currentEditorContent\n        })\n    };\n};\nvar LexicalEditorState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_11__.createContainer)(useLexicalEditor);\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditorState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/lexical-editor.ts\n"));

/***/ })

});