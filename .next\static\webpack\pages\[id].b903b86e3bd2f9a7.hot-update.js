"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/lexical-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/lexical-editor.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"./node_modules/@lexical/react/LexicalComposer.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"./node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"./node_modules/@lexical/react/LexicalContentEditable.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"./node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"./node_modules/@lexical/react/LexicalAutoFocusPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"./node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @lexical/react/LexicalLinkPlugin */ \"./node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @lexical/react/LexicalListPlugin */ \"./node_modules/@lexical/react/LexicalListPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @lexical/react/LexicalMarkdownShortcutPlugin */ \"./node_modules/@lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs\");\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @lexical/markdown */ \"./node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"./node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/rich-text */ \"./node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/code */ \"./node_modules/@lexical/code/LexicalCode.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-div-100vh */ \"./node_modules/react-div-100vh/dist/esm/index.js\");\n/* harmony import */ var libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/hooks/use-mounted */ \"./libs/web/hooks/use-mounted.ts\");\n/* harmony import */ var libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/hooks/use-i18n */ \"./libs/web/hooks/use-i18n.tsx\");\n/* harmony import */ var _plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plugins/slash-commands-plugin */ \"./components/editor/plugins/slash-commands-plugin.tsx\");\n/* harmony import */ var _plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./plugins/floating-toolbar-plugin */ \"./components/editor/plugins/floating-toolbar-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @lexical/react/LexicalCheckListPlugin */ \"./node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs\");\n/* harmony import */ var _plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./plugins/highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var _plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./plugins/image-plugin */ \"./components/editor/plugins/image-plugin.tsx\");\n/* harmony import */ var _plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./plugins/ime-plugin */ \"./components/editor/plugins/ime-plugin.tsx\");\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"./node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRulePlugin */ \"./node_modules/@lexical/react/LexicalHorizontalRulePlugin.dev.mjs\");\n/**\n * Lexical Editor Component\n * Migrated from TipTap to Lexical for better performance and modern architecture\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import custom plugins and nodes\n\n\n\n\n\n\n\n\n\nvar theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\",\n    quote: \"editor-quote\",\n    heading: {\n        h1: \"editor-heading-h1\",\n        h2: \"editor-heading-h2\",\n        h3: \"editor-heading-h3\",\n        h4: \"editor-heading-h4\",\n        h5: \"editor-heading-h5\",\n        h6: \"editor-heading-h6\"\n    },\n    list: {\n        nested: {\n            listitem: \"editor-nested-listitem\"\n        },\n        ol: \"editor-list-ol\",\n        ul: \"editor-list-ul\",\n        listitem: \"editor-listitem\",\n        // 按深度定义不同的列表样式 - 这是关键！\n        olDepth: [\n            \"editor-list-ol-1\",\n            \"editor-list-ol-2\",\n            \"editor-list-ol-3\",\n            \"editor-list-ol-4\",\n            \"editor-list-ol-5\" // I, II, III...\n        ],\n        ulDepth: [\n            \"editor-list-ul-1\",\n            \"editor-list-ul-2\",\n            \"editor-list-ul-3\",\n            \"editor-list-ul-4\",\n            \"editor-list-ul-5\" // ‣\n        ],\n        checklist: \"PlaygroundEditorTheme__checklist\",\n        listitemChecked: \"PlaygroundEditorTheme__listItemChecked\",\n        listitemUnchecked: \"PlaygroundEditorTheme__listItemUnchecked\"\n    },\n    // Lexical 0.32.1 原生缩进支持\n    indent: \"lexical-indent\",\n    image: \"editor-image\",\n    link: \"editor-link\",\n    text: {\n        bold: \"editor-text-bold\",\n        italic: \"editor-text-italic\",\n        overflowed: \"editor-text-overflowed\",\n        hashtag: \"editor-text-hashtag\",\n        underline: \"editor-text-underline\",\n        strikethrough: \"editor-text-strikethrough\",\n        underlineStrikethrough: \"editor-text-underlineStrikethrough\",\n        code: \"editor-text-code\",\n        highlight: \"editor-text-highlight\"\n    },\n    code: \"editor-code\",\n    codeHighlight: {\n        atrule: \"editor-tokenAttr\",\n        attr: \"editor-tokenAttr\",\n        boolean: \"editor-tokenProperty\",\n        builtin: \"editor-tokenSelector\",\n        cdata: \"editor-tokenComment\",\n        char: \"editor-tokenSelector\",\n        class: \"editor-tokenFunction\",\n        \"class-name\": \"editor-tokenFunction\",\n        comment: \"editor-tokenComment\",\n        constant: \"editor-tokenProperty\",\n        deleted: \"editor-tokenProperty\",\n        doctype: \"editor-tokenComment\",\n        entity: \"editor-tokenOperator\",\n        function: \"editor-tokenFunction\",\n        important: \"editor-tokenVariable\",\n        inserted: \"editor-tokenSelector\",\n        keyword: \"editor-tokenAttr\",\n        namespace: \"editor-tokenVariable\",\n        number: \"editor-tokenProperty\",\n        operator: \"editor-tokenOperator\",\n        prolog: \"editor-tokenComment\",\n        property: \"editor-tokenProperty\",\n        punctuation: \"editor-tokenPunctuation\",\n        regex: \"editor-tokenVariable\",\n        selector: \"editor-tokenSelector\",\n        string: \"editor-tokenSelector\",\n        symbol: \"editor-tokenProperty\",\n        tag: \"editor-tokenProperty\",\n        url: \"editor-tokenOperator\",\n        variable: \"editor-tokenVariable\"\n    }\n};\nfunction Placeholder() {\n    _s();\n    var t = (0,libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"])().t;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: t(\"Start writing...\")\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 153,\n        columnNumber: 12\n    }, this);\n}\n_s(Placeholder, \"82N5KF9nLzZ6+2WH7KIjzIXRkLw=\", false, function() {\n    return [\n        libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Placeholder;\nvar LexicalEditor = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(_c1 = _s1(function(param, ref) {\n    var _readOnly = param.readOnly, readOnly = _readOnly === void 0 ? false : _readOnly, _value = param.value, value = _value === void 0 ? \"\" : _value, onChange = param.onChange, onClickLink = param.onClickLink, onHoverLink = param.onHoverLink, _className = param.className, className = _className === void 0 ? \"\" : _className;\n    _s1();\n    var _s = $RefreshSig$(), _s2 = $RefreshSig$();\n    var height = (0,react_div_100vh__WEBPACK_IMPORTED_MODULE_4__.use100vh)();\n    var mounted = (0,libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // 创建一个 ref 来存储编辑器实例\n    var editorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    var initialConfig = {\n        namespace: \"LexicalEditor\",\n        theme: theme,\n        onError: function onError(error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n            // Lexical原生的ListItemNode已经支持checkbox功能\n            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n        ],\n        editable: !readOnly,\n        // 设置初始内容 - 如果有值就解析，否则创建空状态\n        editorState: value && value.trim() ? function() {\n            try {\n                var editorStateData = JSON.parse(value);\n                var tempEditor = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.createEditor)({\n                    namespace: \"TempEditor\",\n                    nodes: [\n                        _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n                        _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n                        _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n                        _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n                        _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n                        _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n                        _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n                        _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n                        _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n                        _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n                    ],\n                    onError: function(error) {\n                        return console.error(\"Temp Editor Error:\", error);\n                    }\n                });\n                return tempEditor.parseEditorState(editorStateData);\n            } catch (error) {\n                console.error(\"Failed to parse initial editor state:\", error);\n                return null;\n            }\n        }() : null\n    };\n    // 创建自定义transformers，包含图片支持\n    var IMAGE_TRANSFORMER = {\n        dependencies: [\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode\n        ],\n        export: function(node) {\n            if (!(0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$isImageNode)(node)) {\n                return null;\n            }\n            return \"![\".concat(node.getAltText(), \"](\").concat(node.getSrc(), \")\");\n        },\n        regExp: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/,\n        replace: function(parentNode, children, match) {\n            var _match = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(match, 3), altText = _match[1], src = _match[2];\n            var imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$createImageNode)({\n                altText: altText,\n                src: src,\n                maxWidth: 800\n            });\n            children.forEach(function(child) {\n                return child.remove();\n            });\n            parentNode.append(imageNode);\n        },\n        type: \"element\"\n    };\n    // 创建自定义的下划线转换器，使用 <u>text</u> 语法\n    var UNDERLINE_TRANSFORMER = {\n        format: [\n            \"underline\"\n        ],\n        tag: \"<u>\",\n        type: \"text-format\"\n    };\n    // 移除段落缩进转换器 - 专注于列表缩进功能\n    // 段落缩进不是标准markdown语法，我们只处理列表缩进\n    // 创建水平分割线转换器\n    var HR_TRANSFORMER = {\n        dependencies: [\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode\n        ],\n        export: function(node) {\n            return (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$isHorizontalRuleNode)(node) ? \"---\" : null;\n        },\n        regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n        replace: function(parentNode, children, match, isImport) {\n            var line = (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$createHorizontalRuleNode)();\n            if (isImport || parentNode.getNextSibling() != null) {\n                parentNode.replace(line);\n            } else {\n                parentNode.insertBefore(line);\n            }\n            line.selectNext();\n        },\n        type: \"element\"\n    };\n    // 🔧 JSON格式保存 - 完全使用Lexical原生格式\n    // 使用标准的Lexical transformers\n    var customTransformers = (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__[\"default\"])(_lexical_markdown__WEBPACK_IMPORTED_MODULE_20__.TRANSFORMERS).concat([\n        // 添加我们的自定义转换器\n        HR_TRANSFORMER,\n        UNDERLINE_TRANSFORMER,\n        IMAGE_TRANSFORMER\n    ]);\n    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题\n    // 简化的 onChange 处理 - 只负责将编辑器状态转换为 JSON 并传递给外部\n    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(editorState, _editor, tags) {\n        if (onChange) {\n            // 忽略历史合并触发的更新\n            if (tags.has(\"history-merge\")) {\n                return;\n            }\n            editorState.read(function() {\n                try {\n                    // 使用Lexical原生JSON格式\n                    var editorStateJSON = JSON.stringify(editorState.toJSON());\n                    // 直接传递给外部，不做任何对比或处理\n                    onChange(function() {\n                        return editorStateJSON;\n                    });\n                } catch (error) {\n                    console.error(\"Error in handleChange:\", error);\n                }\n            });\n        }\n    }, [\n        onChange\n    ]);\n    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑\n    var ListExitPlugin = function() {\n        _s();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            return editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_17__.KEY_ENTER_COMMAND, function(event) {\n                var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getSelection)();\n                if (!(0,lexical__WEBPACK_IMPORTED_MODULE_17__.$isRangeSelection)(selection)) {\n                    return false;\n                }\n                var anchorNode = selection.anchor.getNode();\n                // 检查是否在空的列表项中\n                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(anchorNode)) {\n                    var textContent = anchorNode.getTextContent().trim();\n                    if (textContent === \"\") {\n                        var listNode = anchorNode.getParent();\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(listNode)) {\n                            // 如果是空的列表项，退出列表\n                            event === null || event === void 0 ? void 0 : event.preventDefault();\n                            // 创建新段落并在列表后插入\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                            listNode.insertAfter(paragraph);\n                            // 删除空的列表项\n                            anchorNode.remove();\n                            // 选中新段落\n                            paragraph.select();\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            }, lexical__WEBPACK_IMPORTED_MODULE_17__.COMMAND_PRIORITY_HIGH);\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s(ListExitPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 移除复杂的内容同步插件，完全依赖原生 Lexical 初始化\n    // 编辑器引用组件 - 获取编辑器实例\n    var EditorRefPlugin = function() {\n        _s2();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            editorRef.current = editor;\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s2(EditorRefPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function() {\n        return {\n            focusAtEnd: function() {\n                // 实现焦点到编辑器末尾\n                if (editorRef.current) {\n                    editorRef.current.focus(function() {\n                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                        var lastChild = root.getLastChild();\n                        if (lastChild) {\n                            lastChild.selectEnd();\n                        } else {\n                            root.selectEnd();\n                        }\n                    });\n                }\n            },\n            focusAtStart: function() {\n                // 实现焦点到编辑器开始\n                if (editorRef.current) {\n                    editorRef.current.focus(function() {\n                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                        var firstChild = root.getFirstChild();\n                        if (firstChild) {\n                            firstChild.selectStart();\n                        } else {\n                            root.selectStart();\n                        }\n                    });\n                }\n            }\n        };\n    });\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"b6e8ec94f89652f1\",\n                [\n                    height ? height + \"px\" : \"100vh\"\n                ]\n            ]\n        ]) + \" \" + \"lexical-editor \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__.LexicalComposer, {\n                initialConfig: initialConfig,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"b6e8ec94f89652f1\",\n                            [\n                                height ? height + \"px\" : \"100vh\"\n                            ]\n                        ]\n                    ]) + \" \" + \"editor-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__.ContentEditable, {\n                                className: \"editor-input focus:outline-none w-full\",\n                                spellCheck: false\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__.LinkPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__.ListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__.CheckListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__.MarkdownShortcutPlugin, {\n                            transformers: customTransformers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__.HorizontalRulePlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            enabled: true,\n                            debug: \"development\" === \"development\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListExitPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditorRefPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__.OnChangePlugin, {\n                            onChange: handleChange,\n                            ignoreHistoryMergeTagChange: true,\n                            ignoreSelectionChange: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 21\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                lineNumber: 410,\n                columnNumber: 13\n            }, _this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"b6e8ec94f89652f1\",\n                dynamic: [\n                    height ? height + \"px\" : \"100vh\"\n                ],\n                children: \".lexical-editor{position:relative}.editor-container{position:relative}.editor-input{outline:none;padding:1rem 0;min-height:-webkit-calc(\".concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:-moz-calc(\").concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:calc(\").concat(height ? height + \"px\" : \"100vh\", ' - 14rem);padding-bottom:10rem;width:100%;max-width:none;line-height:1.7;font-size:1rem;color:inherit;-webkit-spellcheck:false;-moz-spellcheck:false;-ms-spellcheck:false;spellcheck:false}.lexical-editor{--lexical-indent-base-value:32px}.lexical-indent{--lexical-indent-base-value:32px}.editor-input p[style*=\"margin-left\"],.editor-input h1[style*=\"margin-left\"],.editor-input h2[style*=\"margin-left\"],.editor-input h3[style*=\"margin-left\"],.editor-input h4[style*=\"margin-left\"],.editor-input h5[style*=\"margin-left\"],.editor-input h6[style*=\"margin-left\"],.editor-input li[style*=\"margin-left\"]{-webkit-transition:margin-left.2s ease;-moz-transition:margin-left.2s ease;-o-transition:margin-left.2s ease;transition:margin-left.2s ease}.editor-placeholder{color:#999;overflow:hidden;position:absolute;-o-text-overflow:ellipsis;text-overflow:ellipsis;top:1rem;left:0;font-size:1rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;pointer-events:none}.editor-paragraph{margin:1.2rem 0;line-height:1.8}.editor-heading-h1{font-size:2.8em;font-weight:bold;margin:1.5rem 0 1rem 0;line-height:1.2}.editor-heading-h2{font-size:2.2em;font-weight:bold;margin:1.4rem 0 .8rem 0;line-height:1.3}.editor-heading-h3{font-size:1.8em;font-weight:bold;margin:1.3rem 0 .6rem 0;line-height:1.4}.editor-heading-h4{font-size:1.5em;font-weight:bold;margin:1.2rem 0 .5rem 0;line-height:1.4}.editor-heading-h5{font-size:1.3em;font-weight:bold;margin:1.1rem 0 .4rem 0;line-height:1.5}.editor-heading-h6{font-size:1.2em;font-weight:bold;margin:1rem 0 .3rem 0;line-height:1.5}.editor-quote{margin:1rem 0;padding-left:1rem;border-left:4px solid#ccc;font-style:italic;color:#666}.editor-list-ol,.editor-list-ul{margin:1rem 0;padding-left:2rem}.editor-listitem{margin:.5rem 0}.editor-link{color:#3b82f6;text-decoration:underline;cursor:pointer}.editor-link:hover{color:#1d4ed8}.editor-text-bold{font-weight:bold}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.editor-text-strikethrough{text-decoration:line-through}.editor-text-code{background-color:#e4e4e7;color:black;padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:monospace;font-size:.9em}.editor-code{background-color:#e4e4e7;color:black;border:1px solid#e9ecef;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;padding:1rem;margin:1rem 0;font-family:\"Courier New\",Courier,monospace;font-size:.9em;line-height:1.4;overflow-x:auto}[data-theme=\"dark\"] .editor-text-code{background-color:#3f3f46;color:white}[data-theme=\"dark\"] .editor-code{background-color:#3f3f46;color:white;border-color:#4b5563}.editor-list-ol{list-style-type:decimal;margin:.5rem 0;padding-left:1.5rem}.editor-list-ul{list-style-type:disc;margin:.5rem 0;padding-left:1.5rem}.editor-listitem{margin:.4rem 0;line-height:1.8}.editor-nested-listitem{margin:.3rem 0}.editor-list-ol-1{list-style-type:decimal}.editor-list-ol-2{list-style-type:lower-alpha}.editor-list-ol-3{list-style-type:lower-roman}.editor-list-ol-4{list-style-type:upper-alpha}.editor-list-ol-5{list-style-type:upper-roman}.editor-list-ul-1{list-style-type:disc}.editor-list-ul-2{list-style-type:circle}.editor-list-ul-3{list-style-type:square}.editor-list-ul-4{list-style-type:disc}.editor-list-ul-5{list-style-type:circle}.task-list{list-style:none;padding-left:0;margin:1rem 0}.task-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;margin:.5rem 0;list-style:none}.task-checkbox{margin-right:.5rem;margin-top:.125rem;cursor:pointer;width:1rem;height:1rem;border:1px solid#d1d5db;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;background:white;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-appearance:none}.task-checkbox:checked{background-color:#3b82f6;border-color:#3b82f6;color:white}.task-checkbox:checked::after{content:\"✓\";font-size:.75rem;font-weight:bold;color:white}.task-content{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:1.7}.task-item[data-checked=\"true\"] .task-content{text-decoration:line-through;opacity:.6}.lexical-highlight,mark,.editor-text-highlight{background-color:#eab834!important;color:black!important;padding:.1rem .2rem;-webkit-border-radius:.125rem;-moz-border-radius:.125rem;border-radius:.125rem}.dark .lexical-highlight,.dark mark,.dark .editor-text-highlight,[data-theme=\"dark\"] .lexical-highlight,[data-theme=\"dark\"] mark,[data-theme=\"dark\"] .editor-text-highlight,html.dark .lexical-highlight,html.dark mark,html.dark .editor-text-highlight{background-color:#3185eb!important;color:white!important}.editor-image img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}@media(prefers-color-scheme:dark){.editor-placeholder{color:#6b7280}.editor-quote{border-left-color:#4b5563;color:#9ca3af}.editor-text-code{background-color:#374151;color:#f9fafb}.editor-code{background-color:#1f2937;border-color:#374151;color:#f9fafb}}.lexical-editor ul:not([data-lexical-list-type=\"check\"]) li::marker{color:#6b7280}.lexical-editor ol li::marker{color:#6b7280}.lexical-editor .PlaygroundEditorTheme__listItemChecked,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked{position:relative;margin-left:.5em;margin-right:.5em;padding-left:1.5em;padding-right:1.5em;list-style-type:none;outline:none;display:block;min-height:1.5em}.lexical-editor .PlaygroundEditorTheme__listItemChecked>*,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked>*{margin-left:.01em}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{content:\"\";width:.9em;height:.9em;top:50%;left:0;cursor:pointer;display:block;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover;position:absolute;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.lexical-editor .PlaygroundEditorTheme__listItemChecked{text-decoration:line-through}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before{-webkit-box-shadow:0 0 0 2px#a6cdfe;-moz-box-shadow:0 0 0 2px#a6cdfe;box-shadow:0 0 0 2px#a6cdfe;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before{border:1px solid#999;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{border:1px solid rgb(61,135,245);-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;background-color:#3d87f5;background-repeat:no-repeat}.lexical-editor .PlaygroundEditorTheme__listItemChecked:after{content:\"\";cursor:pointer;border-color:#fff;border-style:solid;position:absolute;display:block;top:45%;width:.2em;left:.35em;height:.4em;-webkit-transform:translateY(-50%)rotate(45deg);-moz-transform:translateY(-50%)rotate(45deg);-ms-transform:translateY(-50%)rotate(45deg);-o-transform:translateY(-50%)rotate(45deg);transform:translateY(-50%)rotate(45deg);border-width:0 .1em .1em 0}')\n            }, void 0, false, void 0, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 409,\n        columnNumber: 9\n    }, _this);\n}, \"oS0wBeFl4MEW/6I6OJbEwOv9UcA=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n})), \"oS0wBeFl4MEW/6I6OJbEwOv9UcA=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c2 = LexicalEditor;\nLexicalEditor.displayName = \"LexicalEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Placeholder\");\n$RefreshReg$(_c1, \"LexicalEditor$forwardRef\");\n$RefreshReg$(_c2, \"LexicalEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-editor.tsx\n"));

/***/ })

});