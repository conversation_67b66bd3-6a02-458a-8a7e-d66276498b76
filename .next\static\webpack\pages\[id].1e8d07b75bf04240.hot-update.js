"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/state/lexical-editor.ts":
/*!******************************************!*\
  !*** ./libs/web/state/lexical-editor.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\n\n\n\n\nvar ROOT_ID = \"root\";\nvar useLexicalEditor = function(initNote) {\n    // Use initNote if provided, otherwise try to get from NoteState\n    var note = initNote;\n    var createNoteWithTitle, updateNote, createNote;\n    try {\n        var noteState = libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__[\"default\"].useContainer();\n        createNoteWithTitle = noteState.createNoteWithTitle;\n        updateNote = noteState.updateNote;\n        createNote = noteState.createNote;\n        // Only use noteState.note if no initNote is provided\n        if (!note) {\n            note = noteState.note;\n        }\n    } catch (error) {\n        // If NoteState is not available, we'll work with just the initNote\n        console.warn(\"NoteState not available in LexicalEditorState, using initNote only\");\n        createNoteWithTitle = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        updateNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        createNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n    }\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    var editorEl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 🔧 新增：快照状态管理\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), noteSnapshot = ref[0], setNoteSnapshot = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), currentEditorContent = ref1[0], setCurrentEditorContent = ref1[1];\n    // Manual save function for IndexedDB\n    var saveToIndexedDB = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(data) {\n            var existingNote, baseNote, updatedNote;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            2\n                        ];\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        existingNote = _state.sent();\n                        baseNote = existingNote || note;\n                        updatedNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, baseNote, data);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(note.id, updatedNote)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        note\n    ]);\n    var syncToServer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var isNew, localNote, noteToSave, noteData, item, noteUrl, updatedNote, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_6__.has)(router.query, \"new\");\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        11,\n                        ,\n                        12\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    localNote = _state.sent();\n                    noteToSave = localNote || note;\n                    if (!isNew) return [\n                        3,\n                        7\n                    ];\n                    noteData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, noteToSave), {\n                        pid: router.query.pid || ROOT_ID\n                    });\n                    return [\n                        4,\n                        createNote(noteData)\n                    ];\n                case 3:\n                    item = _state.sent();\n                    if (!item) return [\n                        3,\n                        6\n                    ];\n                    noteUrl = \"/\".concat(item.id);\n                    if (!(router.asPath !== noteUrl)) return [\n                        3,\n                        5\n                    ];\n                    return [\n                        4,\n                        router.replace(noteUrl, undefined, {\n                            shallow: true\n                        })\n                    ];\n                case 4:\n                    _state.sent();\n                    _state.label = 5;\n                case 5:\n                    toast(\"Note saved to server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 6:\n                    return [\n                        3,\n                        10\n                    ];\n                case 7:\n                    return [\n                        4,\n                        updateNote(noteToSave)\n                    ];\n                case 8:\n                    updatedNote = _state.sent();\n                    if (!updatedNote) return [\n                        3,\n                        10\n                    ];\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(updatedNote.id, updatedNote)\n                    ];\n                case 9:\n                    _state.sent();\n                    toast(\"Note updated on server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 10:\n                    return [\n                        3,\n                        12\n                    ];\n                case 11:\n                    error = _state.sent();\n                    toast(\"Failed to save note to server\", \"error\");\n                    return [\n                        2,\n                        false\n                    ];\n                case 12:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), [\n        note,\n        router,\n        createNote,\n        updateNote,\n        toast\n    ]);\n    var onCreateLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(title) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!createNoteWithTitle) return [\n                            2,\n                            \"\"\n                        ];\n                        return [\n                            4,\n                            createNoteWithTitle(title)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (result === null || result === void 0 ? void 0 : result.id) {\n                            return [\n                                2,\n                                \"/\".concat(result.id)\n                            ];\n                        }\n                        return [\n                            2,\n                            \"\"\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNoteWithTitle\n    ]);\n    var onSearchLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(term) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    []\n                ];\n            });\n        });\n        return function(term) {\n            return _ref.apply(this, arguments);\n        };\n    }(), []);\n    var onClickLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(href, event) {\n        if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_3__.isNoteLink)(href)) {\n            event.preventDefault();\n            router.push(href);\n        } else {\n            window.open(href, \"_blank\", \"noopener,noreferrer\");\n        }\n    }, [\n        router\n    ]);\n    var onUploadImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(_file, _id) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                // Image upload is disabled in PostgreSQL version\n                toast(\"Image upload is not supported in this version\", \"error\");\n                throw new Error(\"Image upload is not supported\");\n            });\n        });\n        return function(_file, _id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        toast\n    ]);\n    var onHoverLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(event) {\n        return true;\n    }, []);\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), backlinks = ref2[0], setBackLinks = ref2[1];\n    var getBackLinks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var linkNotes;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(note === null || note === void 0 ? void 0 : note.id);\n                    linkNotes = [];\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        linkNotes\n                    ];\n                    setBackLinks([]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].iterate(function(value) {\n                            var ref;\n                            if ((ref = value.linkIds) === null || ref === void 0 ? void 0 : ref.includes((note === null || note === void 0 ? void 0 : note.id) || \"\")) {\n                                linkNotes.push(value);\n                            }\n                        })\n                    ];\n                case 1:\n                    _state.sent();\n                    setBackLinks(linkNotes);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 🔧 快照初始化逻辑 - 打开笔记时设置JSON快照\n    var initializeSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var cachedNote, snapshotJsonContent, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(\"\\uD83D\\uDD27 开始初始化快照:\", {\n                        noteId: note === null || note === void 0 ? void 0 : note.id\n                    });\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) {\n                        // 新建笔记：快照为空值\n                        console.log(\"\\uD83D\\uDD27 新建笔记，设置空快照\");\n                        setNoteSnapshot(null);\n                        setCurrentEditorContent(\"\");\n                        return [\n                            2\n                        ];\n                    }\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    cachedNote = _state.sent();\n                    snapshotJsonContent = (cachedNote === null || cachedNote === void 0 ? void 0 : cachedNote.content) || \"\";\n                    // 如果缓存为空，尝试从 note 对象获取内容\n                    if (!snapshotJsonContent && note.content) {\n                        snapshotJsonContent = note.content;\n                        console.log(\"\\uD83D\\uDD27 缓存为空，使用 note 对象内容\");\n                    }\n                    console.log(\"\\uD83D\\uDD27 已存在笔记，设置快照:\", {\n                        noteId: note.id,\n                        hasContent: !!snapshotJsonContent,\n                        contentLength: snapshotJsonContent.length,\n                        isJson: snapshotJsonContent.startsWith(\"{\"),\n                        source: (cachedNote === null || cachedNote === void 0 ? void 0 : cachedNote.content) ? \"cache\" : \"note\"\n                    });\n                    setNoteSnapshot(snapshotJsonContent);\n                    setCurrentEditorContent(snapshotJsonContent);\n                    return [\n                        3,\n                        4\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"JSON快照初始化失败:\", error);\n                    // 失败时设置为空快照\n                    setNoteSnapshot(null);\n                    setCurrentEditorContent(\"\");\n                    return [\n                        3,\n                        4\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content\n    ]);\n    // 当笔记ID变化时初始化快照\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        // 添加延迟确保笔记内容已经加载\n        var timer = setTimeout(function() {\n            initializeSnapshot();\n        }, 100);\n        return function() {\n            return clearTimeout(timer);\n        };\n    }, [\n        initializeSnapshot\n    ]);\n    // 简化的 onChange 处理 - 只更新当前编辑器内容，不做其他操作\n    var onEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(getValue) {\n        var jsonContent = getValue();\n        // 只更新当前编辑器内容状态，其他逻辑交给 SaveButton 处理\n        setCurrentEditorContent(jsonContent);\n    }, []);\n    // Function to handle title changes specifically\n    var onTitleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(title) {\n        var ref;\n        (ref = saveToIndexedDB({\n            title: title,\n            updated_at: new Date().toISOString()\n        })) === null || ref === void 0 ? void 0 : ref.catch(function(v) {\n            return console.error(\"Error whilst saving title to IndexedDB: %O\", v);\n        });\n    }, [\n        saveToIndexedDB\n    ]);\n    // 🔧 修复：JSON快照对比功能 - 供SaveButton使用\n    var compareWithSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        // 如果是新建笔记（快照为null），任何JSON内容都算作变化\n        if (noteSnapshot === null) {\n            return currentEditorContent.trim() !== \"\";\n        }\n        // 已存在笔记：比较当前JSON内容与JSON快照\n        var hasChanges = currentEditorContent !== noteSnapshot;\n        return hasChanges;\n    }, [\n        noteSnapshot,\n        currentEditorContent\n    ]);\n    // 🔧 新增：获取当前编辑器状态 - 供SaveButton使用\n    var getEditorState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        return {\n            hasChanges: compareWithSnapshot(),\n            currentContent: currentEditorContent,\n            snapshot: noteSnapshot,\n            isNewNote: noteSnapshot === null\n        };\n    }, [\n        compareWithSnapshot,\n        currentEditorContent,\n        noteSnapshot\n    ]);\n    // 🔧 修复：保存当前JSON内容到IndexedDB - 供SaveButton调用\n    var saveCurrentContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var title, titleInput, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    if (note === null || note === void 0 ? void 0 : note.isDailyNote) {\n                        title = note.title;\n                    } else {\n                        titleInput = document.querySelector(\"h1 textarea\");\n                        if (titleInput && titleInput.value) {\n                            title = titleInput.value.trim();\n                        } else {\n                            // 对于JSON格式，使用现有标题或默认标题\n                            title = (note === null || note === void 0 ? void 0 : note.title) || \"Untitled\";\n                        }\n                    }\n                    // 保存JSON内容到IndexedDB\n                    return [\n                        4,\n                        saveToIndexedDB({\n                            content: currentEditorContent,\n                            title: title,\n                            updated_at: new Date().toISOString()\n                        })\n                    ];\n                case 2:\n                    _state.sent();\n                    // 🔧 关键修复：保存成功后更新快照，使状态变为一致\n                    setNoteSnapshot(currentEditorContent);\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 保存JSON到IndexedDB失败:\", error);\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note,\n        currentEditorContent,\n        saveToIndexedDB\n    ]);\n    return {\n        onCreateLink: onCreateLink,\n        onSearchLink: onSearchLink,\n        onClickLink: onClickLink,\n        onUploadImage: onUploadImage,\n        onHoverLink: onHoverLink,\n        getBackLinks: getBackLinks,\n        onEditorChange: onEditorChange,\n        onTitleChange: onTitleChange,\n        saveToIndexedDB: saveToIndexedDB,\n        syncToServer: syncToServer,\n        backlinks: backlinks,\n        editorEl: editorEl,\n        note: note,\n        // 🔧 新增：快照对比相关功能\n        getEditorState: getEditorState,\n        saveCurrentContent: saveCurrentContent,\n        compareWithSnapshot: compareWithSnapshot,\n        // 🔧 关键修复：返回当前编辑器内容作为编辑器的 value\n        editorNote: (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, note), {\n            content: currentEditorContent\n        })\n    };\n};\nvar LexicalEditorState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_11__.createContainer)(useLexicalEditor);\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditorState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9zdGF0ZS9sZXhpY2FsLWVkaXRvci50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7Ozs7QUFBNEM7QUFDSjtBQU96QjtBQUUwQztBQUNMO0FBR1I7QUFDSTtBQUduQjtBQUc3QixJQUFNVyxPQUFPLEdBQUcsTUFBTTtBQUV0QixJQUFNQyxnQkFBZ0IsR0FBRyxTQUFDQyxRQUFvQixFQUFLO0lBQy9DLGdFQUFnRTtJQUNoRSxJQUFJQyxJQUFJLEdBQUdELFFBQVE7SUFDbkIsSUFBSUUsbUJBQW1CLEVBQU9DLFVBQVUsRUFBT0MsVUFBVTtJQUV6RCxJQUFJO1FBQ0EsSUFBTUMsU0FBUyxHQUFHbEIsd0VBQXNCLEVBQUU7UUFDMUNlLG1CQUFtQixHQUFHRyxTQUFTLENBQUNILG1CQUFtQixDQUFDO1FBQ3BEQyxVQUFVLEdBQUdFLFNBQVMsQ0FBQ0YsVUFBVSxDQUFDO1FBQ2xDQyxVQUFVLEdBQUdDLFNBQVMsQ0FBQ0QsVUFBVSxDQUFDO1FBRWxDLHFEQUFxRDtRQUNyRCxJQUFJLENBQUNILElBQUksRUFBRTtZQUNQQSxJQUFJLEdBQUdJLFNBQVMsQ0FBQ0osSUFBSSxDQUFDO1FBQzFCLENBQUM7SUFDTCxFQUFFLE9BQU9NLEtBQUssRUFBRTtRQUNaLG1FQUFtRTtRQUNuRUMsT0FBTyxDQUFDQyxJQUFJLENBQUMsb0VBQW9FLENBQUMsQ0FBQztRQUNuRlAsbUJBQW1CLGlCQUFHOztnQkFBWVE7O29CQUFBQSxTQUFTO2tCQUFBOztTQUFBLEVBQUM7UUFDNUNQLFVBQVUsaUJBQUc7O2dCQUFZTzs7b0JBQUFBLFNBQVM7a0JBQUE7O1NBQUEsRUFBQztRQUNuQ04sVUFBVSxpQkFBRzs7Z0JBQVlNOztvQkFBQUEsU0FBUztrQkFBQTs7U0FBQSxFQUFDO0lBQ3ZDLENBQUM7SUFFRCxJQUFNQyxNQUFNLEdBQUd2QixzREFBUyxFQUFFO0lBQzFCLElBQU13QixLQUFLLEdBQUdsQixrRUFBUSxFQUFFO0lBQ3hCLElBQU1tQixRQUFRLEdBQUd0Qiw2Q0FBTSxDQUFtQixJQUFJLENBQUM7SUFFL0MsY0FBYztJQUNkLElBQXdDRCxHQUE2QixHQUE3QkEsK0NBQVEsQ0FBZ0IsSUFBSSxDQUFDLEVBQTlEd0IsWUFBWSxHQUFxQnhCLEdBQTZCLEdBQWxELEVBQUV5QixlQUFlLEdBQUl6QixHQUE2QixHQUFqQztJQUNwQyxJQUF3REEsSUFBb0IsR0FBcEJBLCtDQUFRLENBQVMsRUFBRSxDQUFDLEVBQXJFMEIsb0JBQW9CLEdBQTZCMUIsSUFBb0IsR0FBakQsRUFBRTJCLHVCQUF1QixHQUFJM0IsSUFBb0IsR0FBeEI7SUFFcEQscUNBQXFDO0lBQ3JDLElBQU00QixlQUFlLEdBQUc3QixrREFBVzttQkFDL0IsNkZBQU84QixJQUF3QixFQUFLO2dCQUkxQkMsWUFBWSxFQUNaQyxRQUFRLEVBRVJDLFdBQVc7Ozs7d0JBTmpCLElBQUksQ0FBQ3JCLENBQUFBLElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRSxHQUFFOzswQkFBTzt3QkFHRDs7NEJBQU01QixtRUFBaUIsQ0FBQ00sSUFBSSxDQUFDc0IsRUFBRSxDQUFDOzBCQUFBOzt3QkFBL0NILFlBQVksR0FBRyxhQUFnQzt3QkFDL0NDLFFBQVEsR0FBR0QsWUFBWSxJQUFJbkIsSUFBSSxDQUFDO3dCQUVoQ3FCLFdBQVcsR0FBRyxtRkFBS0QsUUFBUSxFQUFLRixJQUFJLENBQUUsQ0FBQzt3QkFFN0M7OzRCQUFNeEIsbUVBQWlCLENBQUNNLElBQUksQ0FBQ3NCLEVBQUUsRUFBRUQsV0FBVyxDQUFDOzBCQUFBOzt3QkFBN0MsYUFBNkMsQ0FBQzs7Ozs7O1FBQ2xELENBQUM7d0JBVk1ILElBQXdCOzs7U0FXL0I7UUFBQ2xCLElBQUk7S0FBQyxDQUNUO0lBRUQsSUFBTXlCLFlBQVksR0FBR3JDLGtEQUFXLGVBQzVCLCtGQUFZO1lBR0ZzQyxLQUFLLEVBR0RDLFNBQVMsRUFDVEMsVUFBVSxFQUdOQyxRQUFRLEVBS1JDLElBQUksRUFHQUMsT0FBTyxFQVFYVixXQUFXLEVBUWhCZixLQUFLOzs7O29CQWpDZCxJQUFJLENBQUNOLENBQUFBLElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRSxHQUFFOzt3QkFBTyxLQUFLO3NCQUFDO29CQUV0QkksS0FBSyxHQUFHOUIsMkNBQUcsQ0FBQ2MsTUFBTSxDQUFDc0IsS0FBSyxFQUFFLEtBQUssQ0FBQyxDQUFDOzs7Ozs7Ozs7b0JBR2pCOzt3QkFBTXRDLG1FQUFpQixDQUFDTSxJQUFJLENBQUNzQixFQUFFLENBQUM7c0JBQUE7O29CQUE1Q0ssU0FBUyxHQUFHLGFBQWdDO29CQUM1Q0MsVUFBVSxHQUFHRCxTQUFTLElBQUkzQixJQUFJLENBQUM7eUJBRWpDMEIsS0FBSyxFQUFMQTs7O3NCQUFLO29CQUNDRyxRQUFRLEdBQUcseUtBQ1ZELFVBQVU7d0JBQ2JLLEdBQUcsRUFBRSxNQUFPLENBQUNELEtBQUssQ0FBQ0MsR0FBRyxJQUFlcEMsT0FBTztzQkFDL0MsQ0FBQztvQkFFVzs7d0JBQU1NLFVBQVUsQ0FBQzBCLFFBQVEsQ0FBQztzQkFBQTs7b0JBQWpDQyxJQUFJLEdBQUcsYUFBMEI7eUJBRW5DQSxJQUFJLEVBQUpBOzs7c0JBQUk7b0JBQ0VDLE9BQU8sR0FBRyxHQUFFLENBQVUsT0FBUkQsSUFBSSxDQUFDUixFQUFFLENBQUUsQ0FBQzt5QkFDMUJaLENBQUFBLE1BQU0sQ0FBQ3dCLE1BQU0sS0FBS0gsT0FBTyxHQUF6QnJCOzs7c0JBQXlCO29CQUN6Qjs7d0JBQU1BLE1BQU0sQ0FBQ3lCLE9BQU8sQ0FBQ0osT0FBTyxFQUFFdEIsU0FBUyxFQUFFOzRCQUFFMkIsT0FBTyxFQUFFLElBQUk7eUJBQUUsQ0FBQztzQkFBQTs7b0JBQTNELGFBQTJELENBQUM7OztvQkFFaEV6QixLQUFLLENBQUMsc0JBQXNCLEVBQUUsU0FBUyxDQUFDLENBQUM7b0JBQ3pDOzt3QkFBTyxJQUFJO3NCQUFDOzs7Ozs7O29CQUdJOzt3QkFBTVQsVUFBVSxDQUFDMEIsVUFBVSxDQUFDO3NCQUFBOztvQkFBMUNQLFdBQVcsR0FBRyxhQUE0Qjt5QkFFNUNBLFdBQVcsRUFBWEE7OztzQkFBVztvQkFDWDs7d0JBQU0zQixtRUFBaUIsQ0FBQzJCLFdBQVcsQ0FBQ0MsRUFBRSxFQUFFRCxXQUFXLENBQUM7c0JBQUE7O29CQUFwRCxhQUFvRCxDQUFDO29CQUNyRFYsS0FBSyxDQUFDLHdCQUF3QixFQUFFLFNBQVMsQ0FBQyxDQUFDO29CQUMzQzs7d0JBQU8sSUFBSTtzQkFBQzs7Ozs7OztvQkFHZkwsS0FBSztvQkFDVkssS0FBSyxDQUFDLCtCQUErQixFQUFFLE9BQU8sQ0FBQyxDQUFDO29CQUNoRDs7d0JBQU8sS0FBSztzQkFBQzs7b0JBR2pCOzt3QkFBTyxLQUFLO3NCQUFDOzs7SUFDakIsQ0FBQyxHQUNEO1FBQUNYLElBQUk7UUFBRVUsTUFBTTtRQUFFUCxVQUFVO1FBQUVELFVBQVU7UUFBRVMsS0FBSztLQUFDLENBQ2hEO0lBRUQsSUFBTTBCLFlBQVksR0FBR2pELGtEQUFXO21CQUM1Qiw2RkFBT2tELEtBQWEsRUFBSztnQkFHZkMsTUFBTTs7Ozt3QkFGWixJQUFJLENBQUN0QyxtQkFBbUIsRUFBRTs7NEJBQU8sRUFBRTswQkFBQzt3QkFFckI7OzRCQUFNQSxtQkFBbUIsQ0FBQ3FDLEtBQUssQ0FBQzswQkFBQTs7d0JBQXpDQyxNQUFNLEdBQUcsYUFBZ0M7d0JBQy9DLElBQUlBLE1BQU0sYUFBTkEsTUFBTSxXQUFJLEdBQVZBLEtBQUFBLENBQVUsR0FBVkEsTUFBTSxDQUFFakIsRUFBRSxFQUFFOzRCQUNaOztnQ0FBUSxHQUFDLENBQVksT0FBVmlCLE1BQU0sQ0FBQ2pCLEVBQUUsQ0FBRTs4QkFBQzt3QkFDM0IsQ0FBQzt3QkFDRDs7NEJBQU8sRUFBRTswQkFBQzs7O1FBQ2QsQ0FBQzt3QkFSTWdCLEtBQWE7OztTQVNwQjtRQUFDckMsbUJBQW1CO0tBQUMsQ0FDeEI7SUFFRCxJQUFNdUMsWUFBWSxHQUFHcEQsa0RBQVc7bUJBQzVCLDZGQUFPcUQsSUFBWSxFQUFLOztnQkFDcEI7OztrQkFBVTs7UUFDZCxDQUFDO3dCQUZNQSxJQUFZOzs7U0FHbkIsRUFBRSxDQUNMO0lBRUQsSUFBTUMsV0FBVyxHQUFHdEQsa0RBQVcsQ0FDM0IsU0FBQ3VELElBQVksRUFBRUMsS0FBc0IsRUFBSztRQUN0QyxJQUFJcEQsNERBQVUsQ0FBQ21ELElBQUksQ0FBQyxFQUFFO1lBQ2xCQyxLQUFLLENBQUNDLGNBQWMsRUFBRSxDQUFDO1lBQ3ZCbkMsTUFBTSxDQUFDb0MsSUFBSSxDQUFDSCxJQUFJLENBQUMsQ0FBQztRQUN0QixPQUFPO1lBQ0hJLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDTCxJQUFJLEVBQUUsUUFBUSxFQUFFLHFCQUFxQixDQUFDLENBQUM7UUFDdkQsQ0FBQztJQUNMLENBQUMsRUFDRDtRQUFDakMsTUFBTTtLQUFDLENBQ1g7SUFFRCxJQUFNdUMsYUFBYSxHQUFHN0Qsa0RBQVc7bUJBQzdCLDZGQUFPOEQsS0FBVyxFQUFFQyxHQUFZLEVBQUs7O2dCQUNqQyxpREFBaUQ7Z0JBQ2pEeEMsS0FBSyxDQUFDLCtDQUErQyxFQUFFLE9BQU8sQ0FBQyxDQUFDO2dCQUNoRSxNQUFNLElBQUl5QyxLQUFLLENBQUMsK0JBQStCLENBQUMsQ0FBQzs7UUFDckQsQ0FBQzt3QkFKTUYsS0FBVyxFQUFFQyxHQUFZOzs7U0FLaEM7UUFBQ3hDLEtBQUs7S0FBQyxDQUNWO0lBRUQsSUFBTTBDLFdBQVcsR0FBR2pFLGtEQUFXLENBQUMsU0FBQ3dELEtBQXNCLEVBQUs7UUFDeEQsT0FBTyxJQUFJLENBQUM7SUFDaEIsQ0FBQyxFQUFFLEVBQUUsQ0FBQztJQUVOLElBQWtDdkQsSUFBMkIsR0FBM0JBLCtDQUFRLEVBQW1CLEVBQXREaUUsU0FBUyxHQUFrQmpFLElBQTJCLEdBQTdDLEVBQUVrRSxZQUFZLEdBQUlsRSxJQUEyQixHQUEvQjtJQUU5QixJQUFNbUUsWUFBWSxHQUFHcEUsa0RBQVcsZUFBQywrRkFBWTtZQUVuQ3FFLFNBQVM7Ozs7b0JBRGZsRCxPQUFPLENBQUNtRCxHQUFHLENBQUMxRCxJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUUsQ0FBQyxDQUFDO29CQUNoQm1DLFNBQVMsS0FBc0IsQ0FBQztvQkFDdEMsSUFBSSxDQUFDekQsQ0FBQUEsSUFBSSxhQUFKQSxJQUFJLFdBQUksR0FBUkEsS0FBQUEsQ0FBUSxHQUFSQSxJQUFJLENBQUVzQixFQUFFLEdBQUU7O3dCQUFPbUMsU0FBUztzQkFBQztvQkFDaENGLFlBQVksSUFBSSxDQUFDO29CQUNqQjs7d0JBQU03RCxtRUFBaUIsQ0FBc0IsU0FBQ2tFLEtBQUssRUFBSztnQ0FDaERBLEdBQWE7NEJBQWpCLElBQUlBLENBQUFBLEdBQWEsR0FBYkEsS0FBSyxDQUFDQyxPQUFPLGNBQWJELEdBQWEsV0FBVSxHQUF2QkEsS0FBQUEsQ0FBdUIsR0FBdkJBLEdBQWEsQ0FBRUUsUUFBUSxDQUFDOUQsQ0FBQUEsSUFBSSxhQUFKQSxJQUFJLFdBQUksR0FBUkEsS0FBQUEsQ0FBUSxHQUFSQSxJQUFJLENBQUVzQixFQUFFLEtBQUksRUFBRSxDQUFDLEVBQUU7Z0NBQ3pDbUMsU0FBUyxDQUFDWCxJQUFJLENBQUNjLEtBQUssQ0FBQyxDQUFDOzRCQUMxQixDQUFDO3dCQUNMLENBQUMsQ0FBQztzQkFBQTs7b0JBSkYsYUFJRSxDQUFDO29CQUNITCxZQUFZLENBQUNFLFNBQVMsQ0FBQyxDQUFDOzs7Ozs7SUFDNUIsQ0FBQyxHQUFFO1FBQUN6RCxJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUU7S0FBQyxDQUFDO0lBRWQsNEJBQTRCO0lBQzVCLElBQU15QyxrQkFBa0IsR0FBRzNFLGtEQUFXLGVBQUMsK0ZBQVk7WUFhckM0RSxVQUFVLEVBQ1pDLG1CQUFtQixFQWlCbEIzRCxLQUFLOzs7O29CQTlCZEMsT0FBTyxDQUFDbUQsR0FBRyxDQUFDLHVCQUFZLEVBQUU7d0JBQUVRLE1BQU0sRUFBRWxFLElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRTtxQkFBRSxDQUFDLENBQUM7b0JBRWhELElBQUksQ0FBQ3RCLENBQUFBLElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRSxHQUFFO3dCQUNYLGFBQWE7d0JBQ2JmLE9BQU8sQ0FBQ21ELEdBQUcsQ0FBQyx5QkFBYyxDQUFDLENBQUM7d0JBQzVCNUMsZUFBZSxDQUFDLElBQUksQ0FBQyxDQUFDO3dCQUN0QkUsdUJBQXVCLENBQUMsRUFBRSxDQUFDLENBQUM7d0JBQzVCOzswQkFBTztvQkFDWCxDQUFDOzs7Ozs7Ozs7b0JBSXNCOzt3QkFBTXRCLG1FQUFpQixDQUFDTSxJQUFJLENBQUNzQixFQUFFLENBQUM7c0JBQUE7O29CQUE3QzBDLFVBQVUsR0FBRyxhQUFnQztvQkFDL0NDLG1CQUFtQixHQUFHRCxDQUFBQSxVQUFVLGFBQVZBLFVBQVUsV0FBUyxHQUFuQkEsS0FBQUEsQ0FBbUIsR0FBbkJBLFVBQVUsQ0FBRUcsT0FBTyxLQUFJLEVBQUUsQ0FBQztvQkFFcEQseUJBQXlCO29CQUN6QixJQUFJLENBQUNGLG1CQUFtQixJQUFJakUsSUFBSSxDQUFDbUUsT0FBTyxFQUFFO3dCQUN0Q0YsbUJBQW1CLEdBQUdqRSxJQUFJLENBQUNtRSxPQUFPLENBQUM7d0JBQ25DNUQsT0FBTyxDQUFDbUQsR0FBRyxDQUFDLGdDQUFxQixDQUFDLENBQUM7b0JBQ3ZDLENBQUM7b0JBRURuRCxPQUFPLENBQUNtRCxHQUFHLENBQUMsMEJBQWUsRUFBRTt3QkFDekJRLE1BQU0sRUFBRWxFLElBQUksQ0FBQ3NCLEVBQUU7d0JBQ2Y4QyxVQUFVLEVBQUUsQ0FBQyxDQUFDSCxtQkFBbUI7d0JBQ2pDSSxhQUFhLEVBQUVKLG1CQUFtQixDQUFDSyxNQUFNO3dCQUN6Q0MsTUFBTSxFQUFFTixtQkFBbUIsQ0FBQ08sVUFBVSxDQUFDLEdBQUcsQ0FBQzt3QkFDM0NDLE1BQU0sRUFBRVQsQ0FBQUEsVUFBVSxhQUFWQSxVQUFVLFdBQVMsR0FBbkJBLEtBQUFBLENBQW1CLEdBQW5CQSxVQUFVLENBQUVHLE9BQU8sSUFBRyxPQUFPLEdBQUcsTUFBTTtxQkFDakQsQ0FBQyxDQUFDO29CQUNIckQsZUFBZSxDQUFDbUQsbUJBQW1CLENBQUMsQ0FBQztvQkFDckNqRCx1QkFBdUIsQ0FBQ2lELG1CQUFtQixDQUFDLENBQUM7Ozs7OztvQkFDeEMzRCxLQUFLO29CQUNWQyxPQUFPLENBQUNELEtBQUssQ0FBQyxjQUFjLEVBQUVBLEtBQUssQ0FBQyxDQUFDO29CQUNyQyxZQUFZO29CQUNaUSxlQUFlLENBQUMsSUFBSSxDQUFDLENBQUM7b0JBQ3RCRSx1QkFBdUIsQ0FBQyxFQUFFLENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7SUFFcEMsQ0FBQyxHQUFFO1FBQUNoQixJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUU7UUFBRXRCLElBQUksYUFBSkEsSUFBSSxXQUFTLEdBQWJBLEtBQUFBLENBQWEsR0FBYkEsSUFBSSxDQUFFbUUsT0FBTztLQUFDLENBQUM7SUFFN0IsZ0JBQWdCO0lBQ2hCNUUsZ0RBQVMsQ0FBQyxXQUFNO1FBQ1osaUJBQWlCO1FBQ2pCLElBQU1tRixLQUFLLEdBQUdDLFVBQVUsQ0FBQyxXQUFNO1lBQzNCWixrQkFBa0IsRUFBRSxDQUFDO1FBQ3pCLENBQUMsRUFBRSxHQUFHLENBQUM7UUFFUCxPQUFPO21CQUFNYSxZQUFZLENBQUNGLEtBQUssQ0FBQztTQUFBLENBQUM7SUFDckMsQ0FBQyxFQUFFO1FBQUNYLGtCQUFrQjtLQUFDLENBQUMsQ0FBQztJQUV6QixzQ0FBc0M7SUFDdEMsSUFBTWMsY0FBYyxHQUFHekYsa0RBQVcsQ0FDOUIsU0FBQzBGLFFBQXNCLEVBQVc7UUFDOUIsSUFBTUMsV0FBVyxHQUFHRCxRQUFRLEVBQUU7UUFFOUIsb0NBQW9DO1FBQ3BDOUQsdUJBQXVCLENBQUMrRCxXQUFXLENBQUMsQ0FBQztJQUN6QyxDQUFDLEVBQ0QsRUFBRSxDQUNMO0lBRUQsZ0RBQWdEO0lBQ2hELElBQU1DLGFBQWEsR0FBRzVGLGtEQUFXLENBQzdCLFNBQUNrRCxLQUFhLEVBQVc7WUFDckJyQixHQUdFO1FBSEZBLENBQUFBLEdBR0UsR0FIRkEsZUFBZSxDQUFDO1lBQ1pxQixLQUFLLEVBQUxBLEtBQUs7WUFDTDJDLFVBQVUsRUFBRSxJQUFJQyxJQUFJLEVBQUUsQ0FBQ0MsV0FBVyxFQUFFO1NBQ3ZDLENBQUMsY0FIRmxFLEdBR0UsV0FBTyxHQUhUQSxLQUFBQSxDQUdTLEdBSFRBLEdBR0UsQ0FBRW1FLEtBQUssQ0FBQyxTQUFDQyxDQUFDO21CQUFLOUUsT0FBTyxDQUFDRCxLQUFLLENBQUMsNENBQTRDLEVBQUUrRSxDQUFDLENBQUM7U0FBQSxDQUFDLENBQUM7SUFDckYsQ0FBQyxFQUNEO1FBQUNwRSxlQUFlO0tBQUMsQ0FDcEI7SUFFRCxrQ0FBa0M7SUFDbEMsSUFBTXFFLG1CQUFtQixHQUFHbEcsa0RBQVcsQ0FBQyxXQUFlO1FBQ25ELGlDQUFpQztRQUNqQyxJQUFJeUIsWUFBWSxLQUFLLElBQUksRUFBRTtZQUN2QixPQUFPRSxvQkFBb0IsQ0FBQ3dFLElBQUksRUFBRSxLQUFLLEVBQUUsQ0FBQztRQUM5QyxDQUFDO1FBRUQsMEJBQTBCO1FBQzFCLElBQU1DLFVBQVUsR0FBR3pFLG9CQUFvQixLQUFLRixZQUFZO1FBQ3hELE9BQU8yRSxVQUFVLENBQUM7SUFDdEIsQ0FBQyxFQUFFO1FBQUMzRSxZQUFZO1FBQUVFLG9CQUFvQjtLQUFDLENBQUM7SUFFeEMsaUNBQWlDO0lBQ2pDLElBQU0wRSxjQUFjLEdBQUdyRyxrREFBVyxDQUFDLFdBQU07UUFDckMsT0FBTztZQUNIb0csVUFBVSxFQUFFRixtQkFBbUIsRUFBRTtZQUNqQ0ksY0FBYyxFQUFFM0Usb0JBQW9CO1lBQ3BDNEUsUUFBUSxFQUFFOUUsWUFBWTtZQUN0QitFLFNBQVMsRUFBRS9FLFlBQVksS0FBSyxJQUFJO1NBQ25DLENBQUM7SUFDTixDQUFDLEVBQUU7UUFBQ3lFLG1CQUFtQjtRQUFFdkUsb0JBQW9CO1FBQUVGLFlBQVk7S0FBQyxDQUFDO0lBRTdELDRDQUE0QztJQUM1QyxJQUFNZ0Ysa0JBQWtCLEdBQUd6RyxrREFBVyxlQUFDLCtGQUE4QjtZQUt6RGtELEtBQUssRUFJQ3dELFVBQVUsRUFvQmZ4RixLQUFLOzs7O29CQTVCZCxJQUFJLENBQUNOLENBQUFBLElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRSxHQUFFOzt3QkFBTyxLQUFLO3NCQUFDOzs7Ozs7Ozs7b0JBS3hCLElBQUl0QixJQUFJLGFBQUpBLElBQUksV0FBYSxHQUFqQkEsS0FBQUEsQ0FBaUIsR0FBakJBLElBQUksQ0FBRStGLFdBQVcsRUFBRTt3QkFDbkJ6RCxLQUFLLEdBQUd0QyxJQUFJLENBQUNzQyxLQUFLLENBQUM7b0JBQ3ZCLE9BQU87d0JBQ0d3RCxVQUFVLEdBQUdFLFFBQVEsQ0FBQ0MsYUFBYSxDQUFDLGFBQWEsQ0FBQyxDQUF3Qjt3QkFDaEYsSUFBSUgsVUFBVSxJQUFJQSxVQUFVLENBQUNsQyxLQUFLLEVBQUU7NEJBQ2hDdEIsS0FBSyxHQUFHd0QsVUFBVSxDQUFDbEMsS0FBSyxDQUFDMkIsSUFBSSxFQUFFLENBQUM7d0JBQ3BDLE9BQU87NEJBQ0gsdUJBQXVCOzRCQUN2QmpELEtBQUssR0FBR3RDLENBQUFBLElBQUksYUFBSkEsSUFBSSxXQUFPLEdBQVhBLEtBQUFBLENBQVcsR0FBWEEsSUFBSSxDQUFFc0MsS0FBSyxLQUFJLFVBQVUsQ0FBQzt3QkFDdEMsQ0FBQztvQkFDTCxDQUFDO29CQUVELHFCQUFxQjtvQkFDckI7O3dCQUFNckIsZUFBZSxDQUFDOzRCQUNsQmtELE9BQU8sRUFBRXBELG9CQUFvQjs0QkFDN0J1QixLQUFLLEVBQUxBLEtBQUs7NEJBQ0wyQyxVQUFVLEVBQUUsSUFBSUMsSUFBSSxFQUFFLENBQUNDLFdBQVcsRUFBRTt5QkFDdkMsQ0FBQztzQkFBQTs7b0JBSkYsYUFJRSxDQUFDO29CQUVILDJCQUEyQjtvQkFDM0JyRSxlQUFlLENBQUNDLG9CQUFvQixDQUFDLENBQUM7b0JBRXRDOzt3QkFBTyxJQUFJO3NCQUFDOztvQkFDUFQsS0FBSztvQkFDVkMsT0FBTyxDQUFDRCxLQUFLLENBQUMsa0NBQXVCLEVBQUVBLEtBQUssQ0FBQyxDQUFDO29CQUM5Qzs7d0JBQU8sS0FBSztzQkFBQzs7Ozs7OztJQUVyQixDQUFDLEdBQUU7UUFBQ04sSUFBSTtRQUFFZSxvQkFBb0I7UUFBRUUsZUFBZTtLQUFDLENBQUM7SUFFakQsT0FBTztRQUNIb0IsWUFBWSxFQUFaQSxZQUFZO1FBQ1pHLFlBQVksRUFBWkEsWUFBWTtRQUNaRSxXQUFXLEVBQVhBLFdBQVc7UUFDWE8sYUFBYSxFQUFiQSxhQUFhO1FBQ2JJLFdBQVcsRUFBWEEsV0FBVztRQUNYRyxZQUFZLEVBQVpBLFlBQVk7UUFDWnFCLGNBQWMsRUFBZEEsY0FBYztRQUNkRyxhQUFhLEVBQWJBLGFBQWE7UUFDYi9ELGVBQWUsRUFBZkEsZUFBZTtRQUNmUSxZQUFZLEVBQVpBLFlBQVk7UUFDWjZCLFNBQVMsRUFBVEEsU0FBUztRQUNUMUMsUUFBUSxFQUFSQSxRQUFRO1FBQ1JaLElBQUksRUFBSkEsSUFBSTtRQUNKLGdCQUFnQjtRQUNoQnlGLGNBQWMsRUFBZEEsY0FBYztRQUNkSSxrQkFBa0IsRUFBbEJBLGtCQUFrQjtRQUNsQlAsbUJBQW1CLEVBQW5CQSxtQkFBbUI7UUFDbkIsK0JBQStCO1FBQy9CWSxVQUFVLEVBQUUseUtBQ0xsRyxJQUFJO1lBQ1BtRSxPQUFPLEVBQUVwRCxvQkFBb0I7VUFDaEM7S0FDSixDQUFDO0FBQ04sQ0FBQztBQUVELElBQU1vRixrQkFBa0IsR0FBR3hHLCtEQUFlLENBQUNHLGdCQUFnQixDQUFDO0FBRTVELCtEQUFlcUcsa0JBQWtCLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbGlicy93ZWIvc3RhdGUvbGV4aWNhbC1lZGl0b3IudHM/NDNmOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTm90ZVN0YXRlIGZyb20gJ2xpYnMvd2ViL3N0YXRlL25vdGUnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInO1xuaW1wb3J0IHtcbiAgICB1c2VDYWxsYmFjayxcbiAgICBNb3VzZUV2ZW50IGFzIFJlYWN0TW91c2VFdmVudCxcbiAgICB1c2VTdGF0ZSxcbiAgICB1c2VSZWYsXG4gICAgdXNlRWZmZWN0LFxufSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBzZWFyY2hOb3RlLCBzZWFyY2hSYW5nZVRleHQgfSBmcm9tICdsaWJzL3dlYi91dGlscy9zZWFyY2gnO1xuaW1wb3J0IHsgaXNOb3RlTGluaywgTm90ZU1vZGVsIH0gZnJvbSAnbGlicy9zaGFyZWQvbm90ZSc7XG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ2xpYnMvd2ViL2hvb2tzL3VzZS10b2FzdCc7XG5pbXBvcnQgUG9ydGFsU3RhdGUgZnJvbSAnbGlicy93ZWIvc3RhdGUvcG9ydGFsJztcbmltcG9ydCB7IE5vdGVDYWNoZUl0ZW0gfSBmcm9tICdsaWJzL3dlYi9jYWNoZSc7XG5pbXBvcnQgbm90ZUNhY2hlIGZyb20gJ2xpYnMvd2ViL2NhY2hlL25vdGUnO1xuaW1wb3J0IHsgY3JlYXRlQ29udGFpbmVyIH0gZnJvbSAndW5zdGF0ZWQtbmV4dCc7XG5pbXBvcnQgeyBMZXhpY2FsRWRpdG9yUmVmIH0gZnJvbSAnY29tcG9uZW50cy9lZGl0b3IvbGV4aWNhbC1lZGl0b3InO1xuaW1wb3J0IFVJU3RhdGUgZnJvbSAnbGlicy93ZWIvc3RhdGUvdWknO1xuaW1wb3J0IHsgaGFzIH0gZnJvbSAnbG9kYXNoJztcblxuXG5jb25zdCBST09UX0lEID0gJ3Jvb3QnO1xuXG5jb25zdCB1c2VMZXhpY2FsRWRpdG9yID0gKGluaXROb3RlPzogTm90ZU1vZGVsKSA9PiB7XG4gICAgLy8gVXNlIGluaXROb3RlIGlmIHByb3ZpZGVkLCBvdGhlcndpc2UgdHJ5IHRvIGdldCBmcm9tIE5vdGVTdGF0ZVxuICAgIGxldCBub3RlID0gaW5pdE5vdGU7XG4gICAgbGV0IGNyZWF0ZU5vdGVXaXRoVGl0bGU6IGFueSwgdXBkYXRlTm90ZTogYW55LCBjcmVhdGVOb3RlOiBhbnk7XG5cbiAgICB0cnkge1xuICAgICAgICBjb25zdCBub3RlU3RhdGUgPSBOb3RlU3RhdGUudXNlQ29udGFpbmVyKCk7XG4gICAgICAgIGNyZWF0ZU5vdGVXaXRoVGl0bGUgPSBub3RlU3RhdGUuY3JlYXRlTm90ZVdpdGhUaXRsZTtcbiAgICAgICAgdXBkYXRlTm90ZSA9IG5vdGVTdGF0ZS51cGRhdGVOb3RlO1xuICAgICAgICBjcmVhdGVOb3RlID0gbm90ZVN0YXRlLmNyZWF0ZU5vdGU7XG5cbiAgICAgICAgLy8gT25seSB1c2Ugbm90ZVN0YXRlLm5vdGUgaWYgbm8gaW5pdE5vdGUgaXMgcHJvdmlkZWRcbiAgICAgICAgaWYgKCFub3RlKSB7XG4gICAgICAgICAgICBub3RlID0gbm90ZVN0YXRlLm5vdGU7XG4gICAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAvLyBJZiBOb3RlU3RhdGUgaXMgbm90IGF2YWlsYWJsZSwgd2UnbGwgd29yayB3aXRoIGp1c3QgdGhlIGluaXROb3RlXG4gICAgICAgIGNvbnNvbGUud2FybignTm90ZVN0YXRlIG5vdCBhdmFpbGFibGUgaW4gTGV4aWNhbEVkaXRvclN0YXRlLCB1c2luZyBpbml0Tm90ZSBvbmx5Jyk7XG4gICAgICAgIGNyZWF0ZU5vdGVXaXRoVGl0bGUgPSBhc3luYyAoKSA9PiB1bmRlZmluZWQ7XG4gICAgICAgIHVwZGF0ZU5vdGUgPSBhc3luYyAoKSA9PiB1bmRlZmluZWQ7XG4gICAgICAgIGNyZWF0ZU5vdGUgPSBhc3luYyAoKSA9PiB1bmRlZmluZWQ7XG4gICAgfVxuXG4gICAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gICAgY29uc3QgdG9hc3QgPSB1c2VUb2FzdCgpO1xuICAgIGNvbnN0IGVkaXRvckVsID0gdXNlUmVmPExleGljYWxFZGl0b3JSZWY+KG51bGwpO1xuXG4gICAgLy8g8J+UpyDmlrDlop7vvJrlv6vnhafnirbmgIHnrqHnkIZcbiAgICBjb25zdCBbbm90ZVNuYXBzaG90LCBzZXROb3RlU25hcHNob3RdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gICAgY29uc3QgW2N1cnJlbnRFZGl0b3JDb250ZW50LCBzZXRDdXJyZW50RWRpdG9yQ29udGVudF0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcblxuICAgIC8vIE1hbnVhbCBzYXZlIGZ1bmN0aW9uIGZvciBJbmRleGVkREJcbiAgICBjb25zdCBzYXZlVG9JbmRleGVkREIgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgYXN5bmMgKGRhdGE6IFBhcnRpYWw8Tm90ZU1vZGVsPikgPT4ge1xuICAgICAgICAgICAgaWYgKCFub3RlPy5pZCkgcmV0dXJuO1xuXG4gICAgICAgICAgICAvLyDku44gSW5kZXhlZERCIOiOt+WPluacgOaWsOaVsOaNruS9nOS4uuWfuuehgO+8jOmBv+WFjeimhuebluW3suS/neWtmOeahOaVsOaNrlxuICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdOb3RlID0gYXdhaXQgbm90ZUNhY2hlLmdldEl0ZW0obm90ZS5pZCk7XG4gICAgICAgICAgICBjb25zdCBiYXNlTm90ZSA9IGV4aXN0aW5nTm90ZSB8fCBub3RlO1xuXG4gICAgICAgICAgICBjb25zdCB1cGRhdGVkTm90ZSA9IHsgLi4uYmFzZU5vdGUsIC4uLmRhdGEgfTtcblxuICAgICAgICAgICAgYXdhaXQgbm90ZUNhY2hlLnNldEl0ZW0obm90ZS5pZCwgdXBkYXRlZE5vdGUpO1xuICAgICAgICB9LFxuICAgICAgICBbbm90ZV1cbiAgICApO1xuXG4gICAgY29uc3Qgc3luY1RvU2VydmVyID0gdXNlQ2FsbGJhY2soXG4gICAgICAgIGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgIGlmICghbm90ZT8uaWQpIHJldHVybiBmYWxzZTtcblxuICAgICAgICAgICAgY29uc3QgaXNOZXcgPSBoYXMocm91dGVyLnF1ZXJ5LCAnbmV3Jyk7XG5cbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgbG9jYWxOb3RlID0gYXdhaXQgbm90ZUNhY2hlLmdldEl0ZW0obm90ZS5pZCk7XG4gICAgICAgICAgICAgICAgY29uc3Qgbm90ZVRvU2F2ZSA9IGxvY2FsTm90ZSB8fCBub3RlO1xuXG4gICAgICAgICAgICAgICAgaWYgKGlzTmV3KSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5vdGVEYXRhID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgLi4ubm90ZVRvU2F2ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHBpZDogKHJvdXRlci5xdWVyeS5waWQgYXMgc3RyaW5nKSB8fCBST09UX0lEXG4gICAgICAgICAgICAgICAgICAgIH07XG5cbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaXRlbSA9IGF3YWl0IGNyZWF0ZU5vdGUobm90ZURhdGEpO1xuXG4gICAgICAgICAgICAgICAgICAgIGlmIChpdGVtKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBub3RlVXJsID0gYC8ke2l0ZW0uaWR9YDtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyb3V0ZXIuYXNQYXRoICE9PSBub3RlVXJsKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgcm91dGVyLnJlcGxhY2Uobm90ZVVybCwgdW5kZWZpbmVkLCB7IHNoYWxsb3c6IHRydWUgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB0b2FzdCgnTm90ZSBzYXZlZCB0byBzZXJ2ZXInLCAnc3VjY2VzcycpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkTm90ZSA9IGF3YWl0IHVwZGF0ZU5vdGUobm90ZVRvU2F2ZSk7XG5cbiAgICAgICAgICAgICAgICAgICAgaWYgKHVwZGF0ZWROb3RlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBub3RlQ2FjaGUuc2V0SXRlbSh1cGRhdGVkTm90ZS5pZCwgdXBkYXRlZE5vdGUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgdG9hc3QoJ05vdGUgdXBkYXRlZCBvbiBzZXJ2ZXInLCAnc3VjY2VzcycpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIHRvYXN0KCdGYWlsZWQgdG8gc2F2ZSBub3RlIHRvIHNlcnZlcicsICdlcnJvcicpO1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9LFxuICAgICAgICBbbm90ZSwgcm91dGVyLCBjcmVhdGVOb3RlLCB1cGRhdGVOb3RlLCB0b2FzdF1cbiAgICApO1xuXG4gICAgY29uc3Qgb25DcmVhdGVMaW5rID0gdXNlQ2FsbGJhY2soXG4gICAgICAgIGFzeW5jICh0aXRsZTogc3RyaW5nKSA9PiB7XG4gICAgICAgICAgICBpZiAoIWNyZWF0ZU5vdGVXaXRoVGl0bGUpIHJldHVybiAnJztcblxuICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY3JlYXRlTm90ZVdpdGhUaXRsZSh0aXRsZSk7XG4gICAgICAgICAgICBpZiAocmVzdWx0Py5pZCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBgLyR7cmVzdWx0LmlkfWA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gJyc7XG4gICAgICAgIH0sXG4gICAgICAgIFtjcmVhdGVOb3RlV2l0aFRpdGxlXVxuICAgICk7XG5cbiAgICBjb25zdCBvblNlYXJjaExpbmsgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgYXN5bmMgKHRlcm06IHN0cmluZykgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIFtdO1xuICAgICAgICB9LFxuICAgICAgICBbXVxuICAgICk7XG5cbiAgICBjb25zdCBvbkNsaWNrTGluayA9IHVzZUNhbGxiYWNrKFxuICAgICAgICAoaHJlZjogc3RyaW5nLCBldmVudDogUmVhY3RNb3VzZUV2ZW50KSA9PiB7XG4gICAgICAgICAgICBpZiAoaXNOb3RlTGluayhocmVmKSkge1xuICAgICAgICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgICAgcm91dGVyLnB1c2goaHJlZik7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHdpbmRvdy5vcGVuKGhyZWYsICdfYmxhbmsnLCAnbm9vcGVuZXIsbm9yZWZlcnJlcicpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBbcm91dGVyXVxuICAgICk7XG5cbiAgICBjb25zdCBvblVwbG9hZEltYWdlID0gdXNlQ2FsbGJhY2soXG4gICAgICAgIGFzeW5jIChfZmlsZTogRmlsZSwgX2lkPzogc3RyaW5nKSA9PiB7XG4gICAgICAgICAgICAvLyBJbWFnZSB1cGxvYWQgaXMgZGlzYWJsZWQgaW4gUG9zdGdyZVNRTCB2ZXJzaW9uXG4gICAgICAgICAgICB0b2FzdCgnSW1hZ2UgdXBsb2FkIGlzIG5vdCBzdXBwb3J0ZWQgaW4gdGhpcyB2ZXJzaW9uJywgJ2Vycm9yJyk7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ltYWdlIHVwbG9hZCBpcyBub3Qgc3VwcG9ydGVkJyk7XG4gICAgICAgIH0sXG4gICAgICAgIFt0b2FzdF1cbiAgICApO1xuXG4gICAgY29uc3Qgb25Ib3ZlckxpbmsgPSB1c2VDYWxsYmFjaygoZXZlbnQ6IFJlYWN0TW91c2VFdmVudCkgPT4ge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9LCBbXSk7XG5cbiAgICBjb25zdCBbYmFja2xpbmtzLCBzZXRCYWNrTGlua3NdID0gdXNlU3RhdGU8Tm90ZUNhY2hlSXRlbVtdPigpO1xuXG4gICAgY29uc3QgZ2V0QmFja0xpbmtzID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZyhub3RlPy5pZCk7XG4gICAgICAgIGNvbnN0IGxpbmtOb3RlczogTm90ZUNhY2hlSXRlbVtdID0gW107XG4gICAgICAgIGlmICghbm90ZT8uaWQpIHJldHVybiBsaW5rTm90ZXM7XG4gICAgICAgIHNldEJhY2tMaW5rcyhbXSk7XG4gICAgICAgIGF3YWl0IG5vdGVDYWNoZS5pdGVyYXRlPE5vdGVDYWNoZUl0ZW0sIHZvaWQ+KCh2YWx1ZSkgPT4ge1xuICAgICAgICAgICAgaWYgKHZhbHVlLmxpbmtJZHM/LmluY2x1ZGVzKG5vdGU/LmlkIHx8ICcnKSkge1xuICAgICAgICAgICAgICAgIGxpbmtOb3Rlcy5wdXNoKHZhbHVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIHNldEJhY2tMaW5rcyhsaW5rTm90ZXMpO1xuICAgIH0sIFtub3RlPy5pZF0pO1xuXG4gICAgLy8g8J+UpyDlv6vnhafliJ3lp4vljJbpgLvovpEgLSDmiZPlvIDnrJTorrDml7borr7nva5KU09O5b+r54WnXG4gICAgY29uc3QgaW5pdGlhbGl6ZVNuYXBzaG90ID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UpyDlvIDlp4vliJ3lp4vljJblv6vnhac6JywgeyBub3RlSWQ6IG5vdGU/LmlkIH0pO1xuXG4gICAgICAgIGlmICghbm90ZT8uaWQpIHtcbiAgICAgICAgICAgIC8vIOaWsOW7uueslOiusO+8muW/q+eFp+S4uuepuuWAvFxuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflKcg5paw5bu656yU6K6w77yM6K6+572u56m65b+r54WnJyk7XG4gICAgICAgICAgICBzZXROb3RlU25hcHNob3QobnVsbCk7XG4gICAgICAgICAgICBzZXRDdXJyZW50RWRpdG9yQ29udGVudCgnJyk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8g5bey5a2Y5Zyo56yU6K6w77ya5LyY5YWI5LuO57yT5a2Y6I635Y+W77yM54S25ZCO5Zue6YCA5YiwIG5vdGUg5a+56LGhXG4gICAgICAgICAgICBjb25zdCBjYWNoZWROb3RlID0gYXdhaXQgbm90ZUNhY2hlLmdldEl0ZW0obm90ZS5pZCk7XG4gICAgICAgICAgICBsZXQgc25hcHNob3RKc29uQ29udGVudCA9IGNhY2hlZE5vdGU/LmNvbnRlbnQgfHwgJyc7XG5cbiAgICAgICAgICAgIC8vIOWmguaenOe8k+WtmOS4uuepuu+8jOWwneivleS7jiBub3RlIOWvueixoeiOt+WPluWGheWuuVxuICAgICAgICAgICAgaWYgKCFzbmFwc2hvdEpzb25Db250ZW50ICYmIG5vdGUuY29udGVudCkge1xuICAgICAgICAgICAgICAgIHNuYXBzaG90SnNvbkNvbnRlbnQgPSBub3RlLmNvbnRlbnQ7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflKcg57yT5a2Y5Li656m677yM5L2/55SoIG5vdGUg5a+56LGh5YaF5a65Jyk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SnIOW3suWtmOWcqOeslOiusO+8jOiuvue9ruW/q+eFpzonLCB7XG4gICAgICAgICAgICAgICAgbm90ZUlkOiBub3RlLmlkLFxuICAgICAgICAgICAgICAgIGhhc0NvbnRlbnQ6ICEhc25hcHNob3RKc29uQ29udGVudCxcbiAgICAgICAgICAgICAgICBjb250ZW50TGVuZ3RoOiBzbmFwc2hvdEpzb25Db250ZW50Lmxlbmd0aCxcbiAgICAgICAgICAgICAgICBpc0pzb246IHNuYXBzaG90SnNvbkNvbnRlbnQuc3RhcnRzV2l0aCgneycpLFxuICAgICAgICAgICAgICAgIHNvdXJjZTogY2FjaGVkTm90ZT8uY29udGVudCA/ICdjYWNoZScgOiAnbm90ZSdcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgc2V0Tm90ZVNuYXBzaG90KHNuYXBzaG90SnNvbkNvbnRlbnQpO1xuICAgICAgICAgICAgc2V0Q3VycmVudEVkaXRvckNvbnRlbnQoc25hcHNob3RKc29uQ29udGVudCk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdKU09O5b+r54Wn5Yid5aeL5YyW5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgICAgIC8vIOWksei0peaXtuiuvue9ruS4uuepuuW/q+eFp1xuICAgICAgICAgICAgc2V0Tm90ZVNuYXBzaG90KG51bGwpO1xuICAgICAgICAgICAgc2V0Q3VycmVudEVkaXRvckNvbnRlbnQoJycpO1xuICAgICAgICB9XG4gICAgfSwgW25vdGU/LmlkLCBub3RlPy5jb250ZW50XSk7XG5cbiAgICAvLyDlvZPnrJTorrBJROWPmOWMluaXtuWIneWni+WMluW/q+eFp1xuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIC8vIOa3u+WKoOW7tui/n+ehruS/neeslOiusOWGheWuueW3sue7j+WKoOi9vVxuICAgICAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgaW5pdGlhbGl6ZVNuYXBzaG90KCk7XG4gICAgICAgIH0sIDEwMCk7XG5cbiAgICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lcik7XG4gICAgfSwgW2luaXRpYWxpemVTbmFwc2hvdF0pO1xuXG4gICAgLy8g566A5YyW55qEIG9uQ2hhbmdlIOWkhOeQhiAtIOWPquabtOaWsOW9k+WJjee8lui+keWZqOWGheWuue+8jOS4jeWBmuWFtuS7luaTjeS9nFxuICAgIGNvbnN0IG9uRWRpdG9yQ2hhbmdlID0gdXNlQ2FsbGJhY2soXG4gICAgICAgIChnZXRWYWx1ZTogKCkgPT4gc3RyaW5nKTogdm9pZCA9PiB7XG4gICAgICAgICAgICBjb25zdCBqc29uQ29udGVudCA9IGdldFZhbHVlKCk7XG5cbiAgICAgICAgICAgIC8vIOWPquabtOaWsOW9k+WJjee8lui+keWZqOWGheWuueeKtuaAge+8jOWFtuS7lumAu+i+keS6pOe7mSBTYXZlQnV0dG9uIOWkhOeQhlxuICAgICAgICAgICAgc2V0Q3VycmVudEVkaXRvckNvbnRlbnQoanNvbkNvbnRlbnQpO1xuICAgICAgICB9LFxuICAgICAgICBbXVxuICAgICk7XG5cbiAgICAvLyBGdW5jdGlvbiB0byBoYW5kbGUgdGl0bGUgY2hhbmdlcyBzcGVjaWZpY2FsbHlcbiAgICBjb25zdCBvblRpdGxlQ2hhbmdlID0gdXNlQ2FsbGJhY2soXG4gICAgICAgICh0aXRsZTogc3RyaW5nKTogdm9pZCA9PiB7XG4gICAgICAgICAgICBzYXZlVG9JbmRleGVkREIoe1xuICAgICAgICAgICAgICAgIHRpdGxlLFxuICAgICAgICAgICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgfSk/LmNhdGNoKCh2KSA9PiBjb25zb2xlLmVycm9yKCdFcnJvciB3aGlsc3Qgc2F2aW5nIHRpdGxlIHRvIEluZGV4ZWREQjogJU8nLCB2KSk7XG4gICAgICAgIH0sXG4gICAgICAgIFtzYXZlVG9JbmRleGVkREJdXG4gICAgKTtcblxuICAgIC8vIPCflKcg5L+u5aSN77yaSlNPTuW/q+eFp+WvueavlOWKn+iDvSAtIOS+m1NhdmVCdXR0b27kvb/nlKhcbiAgICBjb25zdCBjb21wYXJlV2l0aFNuYXBzaG90ID0gdXNlQ2FsbGJhY2soKCk6IGJvb2xlYW4gPT4ge1xuICAgICAgICAvLyDlpoLmnpzmmK/mlrDlu7rnrJTorrDvvIjlv6vnhafkuLpudWxs77yJ77yM5Lu75L2VSlNPTuWGheWuuemDveeul+S9nOWPmOWMllxuICAgICAgICBpZiAobm90ZVNuYXBzaG90ID09PSBudWxsKSB7XG4gICAgICAgICAgICByZXR1cm4gY3VycmVudEVkaXRvckNvbnRlbnQudHJpbSgpICE9PSAnJztcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOW3suWtmOWcqOeslOiusO+8muavlOi+g+W9k+WJjUpTT07lhoXlrrnkuI5KU09O5b+r54WnXG4gICAgICAgIGNvbnN0IGhhc0NoYW5nZXMgPSBjdXJyZW50RWRpdG9yQ29udGVudCAhPT0gbm90ZVNuYXBzaG90O1xuICAgICAgICByZXR1cm4gaGFzQ2hhbmdlcztcbiAgICB9LCBbbm90ZVNuYXBzaG90LCBjdXJyZW50RWRpdG9yQ29udGVudF0pO1xuXG4gICAgLy8g8J+UpyDmlrDlop7vvJrojrflj5blvZPliY3nvJbovpHlmajnirbmgIEgLSDkvptTYXZlQnV0dG9u5L2/55SoXG4gICAgY29uc3QgZ2V0RWRpdG9yU3RhdGUgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBoYXNDaGFuZ2VzOiBjb21wYXJlV2l0aFNuYXBzaG90KCksXG4gICAgICAgICAgICBjdXJyZW50Q29udGVudDogY3VycmVudEVkaXRvckNvbnRlbnQsXG4gICAgICAgICAgICBzbmFwc2hvdDogbm90ZVNuYXBzaG90LFxuICAgICAgICAgICAgaXNOZXdOb3RlOiBub3RlU25hcHNob3QgPT09IG51bGxcbiAgICAgICAgfTtcbiAgICB9LCBbY29tcGFyZVdpdGhTbmFwc2hvdCwgY3VycmVudEVkaXRvckNvbnRlbnQsIG5vdGVTbmFwc2hvdF0pO1xuXG4gICAgLy8g8J+UpyDkv67lpI3vvJrkv53lrZjlvZPliY1KU09O5YaF5a655YiwSW5kZXhlZERCIC0g5L6bU2F2ZUJ1dHRvbuiwg+eUqFxuICAgIGNvbnN0IHNhdmVDdXJyZW50Q29udGVudCA9IHVzZUNhbGxiYWNrKGFzeW5jICgpOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgICAgICAgaWYgKCFub3RlPy5pZCkgcmV0dXJuIGZhbHNlO1xuXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICAvLyDojrflj5bmoIfpophcbiAgICAgICAgICAgIGxldCB0aXRsZTogc3RyaW5nO1xuICAgICAgICAgICAgaWYgKG5vdGU/LmlzRGFpbHlOb3RlKSB7XG4gICAgICAgICAgICAgICAgdGl0bGUgPSBub3RlLnRpdGxlO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBjb25zdCB0aXRsZUlucHV0ID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignaDEgdGV4dGFyZWEnKSBhcyBIVE1MVGV4dEFyZWFFbGVtZW50O1xuICAgICAgICAgICAgICAgIGlmICh0aXRsZUlucHV0ICYmIHRpdGxlSW5wdXQudmFsdWUpIHtcbiAgICAgICAgICAgICAgICAgICAgdGl0bGUgPSB0aXRsZUlucHV0LnZhbHVlLnRyaW0oKTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAvLyDlr7nkuo5KU09O5qC85byP77yM5L2/55So546w5pyJ5qCH6aKY5oiW6buY6K6k5qCH6aKYXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlID0gbm90ZT8udGl0bGUgfHwgJ1VudGl0bGVkJztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIOS/neWtmEpTT07lhoXlrrnliLBJbmRleGVkREJcbiAgICAgICAgICAgIGF3YWl0IHNhdmVUb0luZGV4ZWREQih7XG4gICAgICAgICAgICAgICAgY29udGVudDogY3VycmVudEVkaXRvckNvbnRlbnQsIC8vIOi/memHjOaYr0pTT07moLzlvI9cbiAgICAgICAgICAgICAgICB0aXRsZSxcbiAgICAgICAgICAgICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAvLyDwn5SnIOWFs+mUruS/ruWkje+8muS/neWtmOaIkOWKn+WQjuabtOaWsOW/q+eFp++8jOS9v+eKtuaAgeWPmOS4uuS4gOiHtFxuICAgICAgICAgICAgc2V0Tm90ZVNuYXBzaG90KGN1cnJlbnRFZGl0b3JDb250ZW50KTtcblxuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfwn5SnIOS/neWtmEpTT07liLBJbmRleGVkRELlpLHotKU6JywgZXJyb3IpO1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgfSwgW25vdGUsIGN1cnJlbnRFZGl0b3JDb250ZW50LCBzYXZlVG9JbmRleGVkREJdKTtcblxuICAgIHJldHVybiB7XG4gICAgICAgIG9uQ3JlYXRlTGluayxcbiAgICAgICAgb25TZWFyY2hMaW5rLFxuICAgICAgICBvbkNsaWNrTGluayxcbiAgICAgICAgb25VcGxvYWRJbWFnZSxcbiAgICAgICAgb25Ib3ZlckxpbmssXG4gICAgICAgIGdldEJhY2tMaW5rcyxcbiAgICAgICAgb25FZGl0b3JDaGFuZ2UsXG4gICAgICAgIG9uVGl0bGVDaGFuZ2UsXG4gICAgICAgIHNhdmVUb0luZGV4ZWREQixcbiAgICAgICAgc3luY1RvU2VydmVyLFxuICAgICAgICBiYWNrbGlua3MsXG4gICAgICAgIGVkaXRvckVsLFxuICAgICAgICBub3RlLFxuICAgICAgICAvLyDwn5SnIOaWsOWinu+8muW/q+eFp+WvueavlOebuOWFs+WKn+iDvVxuICAgICAgICBnZXRFZGl0b3JTdGF0ZSxcbiAgICAgICAgc2F2ZUN1cnJlbnRDb250ZW50LFxuICAgICAgICBjb21wYXJlV2l0aFNuYXBzaG90LFxuICAgICAgICAvLyDwn5SnIOWFs+mUruS/ruWkje+8mui/lOWbnuW9k+WJjee8lui+keWZqOWGheWuueS9nOS4uue8lui+keWZqOeahCB2YWx1ZVxuICAgICAgICBlZGl0b3JOb3RlOiB7XG4gICAgICAgICAgICAuLi5ub3RlLFxuICAgICAgICAgICAgY29udGVudDogY3VycmVudEVkaXRvckNvbnRlbnRcbiAgICAgICAgfSxcbiAgICB9O1xufTtcblxuY29uc3QgTGV4aWNhbEVkaXRvclN0YXRlID0gY3JlYXRlQ29udGFpbmVyKHVzZUxleGljYWxFZGl0b3IpO1xuXG5leHBvcnQgZGVmYXVsdCBMZXhpY2FsRWRpdG9yU3RhdGU7XG4iXSwibmFtZXMiOlsiTm90ZVN0YXRlIiwidXNlUm91dGVyIiwidXNlQ2FsbGJhY2siLCJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsImlzTm90ZUxpbmsiLCJ1c2VUb2FzdCIsIm5vdGVDYWNoZSIsImNyZWF0ZUNvbnRhaW5lciIsImhhcyIsIlJPT1RfSUQiLCJ1c2VMZXhpY2FsRWRpdG9yIiwiaW5pdE5vdGUiLCJub3RlIiwiY3JlYXRlTm90ZVdpdGhUaXRsZSIsInVwZGF0ZU5vdGUiLCJjcmVhdGVOb3RlIiwibm90ZVN0YXRlIiwidXNlQ29udGFpbmVyIiwiZXJyb3IiLCJjb25zb2xlIiwid2FybiIsInVuZGVmaW5lZCIsInJvdXRlciIsInRvYXN0IiwiZWRpdG9yRWwiLCJub3RlU25hcHNob3QiLCJzZXROb3RlU25hcHNob3QiLCJjdXJyZW50RWRpdG9yQ29udGVudCIsInNldEN1cnJlbnRFZGl0b3JDb250ZW50Iiwic2F2ZVRvSW5kZXhlZERCIiwiZGF0YSIsImV4aXN0aW5nTm90ZSIsImJhc2VOb3RlIiwidXBkYXRlZE5vdGUiLCJpZCIsImdldEl0ZW0iLCJzZXRJdGVtIiwic3luY1RvU2VydmVyIiwiaXNOZXciLCJsb2NhbE5vdGUiLCJub3RlVG9TYXZlIiwibm90ZURhdGEiLCJpdGVtIiwibm90ZVVybCIsInF1ZXJ5IiwicGlkIiwiYXNQYXRoIiwicmVwbGFjZSIsInNoYWxsb3ciLCJvbkNyZWF0ZUxpbmsiLCJ0aXRsZSIsInJlc3VsdCIsIm9uU2VhcmNoTGluayIsInRlcm0iLCJvbkNsaWNrTGluayIsImhyZWYiLCJldmVudCIsInByZXZlbnREZWZhdWx0IiwicHVzaCIsIndpbmRvdyIsIm9wZW4iLCJvblVwbG9hZEltYWdlIiwiX2ZpbGUiLCJfaWQiLCJFcnJvciIsIm9uSG92ZXJMaW5rIiwiYmFja2xpbmtzIiwic2V0QmFja0xpbmtzIiwiZ2V0QmFja0xpbmtzIiwibGlua05vdGVzIiwibG9nIiwiaXRlcmF0ZSIsInZhbHVlIiwibGlua0lkcyIsImluY2x1ZGVzIiwiaW5pdGlhbGl6ZVNuYXBzaG90IiwiY2FjaGVkTm90ZSIsInNuYXBzaG90SnNvbkNvbnRlbnQiLCJub3RlSWQiLCJjb250ZW50IiwiaGFzQ29udGVudCIsImNvbnRlbnRMZW5ndGgiLCJsZW5ndGgiLCJpc0pzb24iLCJzdGFydHNXaXRoIiwic291cmNlIiwidGltZXIiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0Iiwib25FZGl0b3JDaGFuZ2UiLCJnZXRWYWx1ZSIsImpzb25Db250ZW50Iiwib25UaXRsZUNoYW5nZSIsInVwZGF0ZWRfYXQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJjYXRjaCIsInYiLCJjb21wYXJlV2l0aFNuYXBzaG90IiwidHJpbSIsImhhc0NoYW5nZXMiLCJnZXRFZGl0b3JTdGF0ZSIsImN1cnJlbnRDb250ZW50Iiwic25hcHNob3QiLCJpc05ld05vdGUiLCJzYXZlQ3VycmVudENvbnRlbnQiLCJ0aXRsZUlucHV0IiwiaXNEYWlseU5vdGUiLCJkb2N1bWVudCIsInF1ZXJ5U2VsZWN0b3IiLCJlZGl0b3JOb3RlIiwiTGV4aWNhbEVkaXRvclN0YXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./libs/web/state/lexical-editor.ts\n"));

/***/ })

});