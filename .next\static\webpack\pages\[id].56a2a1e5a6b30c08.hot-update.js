"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/lexical-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/lexical-editor.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"./node_modules/@lexical/react/LexicalComposer.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"./node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"./node_modules/@lexical/react/LexicalContentEditable.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"./node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"./node_modules/@lexical/react/LexicalAutoFocusPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"./node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @lexical/react/LexicalLinkPlugin */ \"./node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @lexical/react/LexicalListPlugin */ \"./node_modules/@lexical/react/LexicalListPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @lexical/react/LexicalMarkdownShortcutPlugin */ \"./node_modules/@lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs\");\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @lexical/markdown */ \"./node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"./node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/rich-text */ \"./node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/code */ \"./node_modules/@lexical/code/LexicalCode.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-div-100vh */ \"./node_modules/react-div-100vh/dist/esm/index.js\");\n/* harmony import */ var libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/hooks/use-mounted */ \"./libs/web/hooks/use-mounted.ts\");\n/* harmony import */ var libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/hooks/use-i18n */ \"./libs/web/hooks/use-i18n.tsx\");\n/* harmony import */ var _plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plugins/slash-commands-plugin */ \"./components/editor/plugins/slash-commands-plugin.tsx\");\n/* harmony import */ var _plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./plugins/floating-toolbar-plugin */ \"./components/editor/plugins/floating-toolbar-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @lexical/react/LexicalCheckListPlugin */ \"./node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs\");\n/* harmony import */ var _plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./plugins/highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var _plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./plugins/image-plugin */ \"./components/editor/plugins/image-plugin.tsx\");\n/* harmony import */ var _plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./plugins/ime-plugin */ \"./components/editor/plugins/ime-plugin.tsx\");\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"./node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRulePlugin */ \"./node_modules/@lexical/react/LexicalHorizontalRulePlugin.dev.mjs\");\n/**\n * Lexical Editor Component\n * Migrated from TipTap to Lexical for better performance and modern architecture\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import custom plugins and nodes\n\n\n\n\n\n\n\n\n\nvar theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\",\n    quote: \"editor-quote\",\n    heading: {\n        h1: \"editor-heading-h1\",\n        h2: \"editor-heading-h2\",\n        h3: \"editor-heading-h3\",\n        h4: \"editor-heading-h4\",\n        h5: \"editor-heading-h5\",\n        h6: \"editor-heading-h6\"\n    },\n    list: {\n        nested: {\n            listitem: \"editor-nested-listitem\"\n        },\n        ol: \"editor-list-ol\",\n        ul: \"editor-list-ul\",\n        listitem: \"editor-listitem\",\n        // 按深度定义不同的列表样式 - 这是关键！\n        olDepth: [\n            \"editor-list-ol-1\",\n            \"editor-list-ol-2\",\n            \"editor-list-ol-3\",\n            \"editor-list-ol-4\",\n            \"editor-list-ol-5\" // I, II, III...\n        ],\n        ulDepth: [\n            \"editor-list-ul-1\",\n            \"editor-list-ul-2\",\n            \"editor-list-ul-3\",\n            \"editor-list-ul-4\",\n            \"editor-list-ul-5\" // ‣\n        ],\n        checklist: \"PlaygroundEditorTheme__checklist\",\n        listitemChecked: \"PlaygroundEditorTheme__listItemChecked\",\n        listitemUnchecked: \"PlaygroundEditorTheme__listItemUnchecked\"\n    },\n    // Lexical 0.32.1 原生缩进支持\n    indent: \"lexical-indent\",\n    image: \"editor-image\",\n    link: \"editor-link\",\n    text: {\n        bold: \"editor-text-bold\",\n        italic: \"editor-text-italic\",\n        overflowed: \"editor-text-overflowed\",\n        hashtag: \"editor-text-hashtag\",\n        underline: \"editor-text-underline\",\n        strikethrough: \"editor-text-strikethrough\",\n        underlineStrikethrough: \"editor-text-underlineStrikethrough\",\n        code: \"editor-text-code\",\n        highlight: \"editor-text-highlight\"\n    },\n    code: \"editor-code\",\n    codeHighlight: {\n        atrule: \"editor-tokenAttr\",\n        attr: \"editor-tokenAttr\",\n        boolean: \"editor-tokenProperty\",\n        builtin: \"editor-tokenSelector\",\n        cdata: \"editor-tokenComment\",\n        char: \"editor-tokenSelector\",\n        class: \"editor-tokenFunction\",\n        \"class-name\": \"editor-tokenFunction\",\n        comment: \"editor-tokenComment\",\n        constant: \"editor-tokenProperty\",\n        deleted: \"editor-tokenProperty\",\n        doctype: \"editor-tokenComment\",\n        entity: \"editor-tokenOperator\",\n        function: \"editor-tokenFunction\",\n        important: \"editor-tokenVariable\",\n        inserted: \"editor-tokenSelector\",\n        keyword: \"editor-tokenAttr\",\n        namespace: \"editor-tokenVariable\",\n        number: \"editor-tokenProperty\",\n        operator: \"editor-tokenOperator\",\n        prolog: \"editor-tokenComment\",\n        property: \"editor-tokenProperty\",\n        punctuation: \"editor-tokenPunctuation\",\n        regex: \"editor-tokenVariable\",\n        selector: \"editor-tokenSelector\",\n        string: \"editor-tokenSelector\",\n        symbol: \"editor-tokenProperty\",\n        tag: \"editor-tokenProperty\",\n        url: \"editor-tokenOperator\",\n        variable: \"editor-tokenVariable\"\n    }\n};\nfunction Placeholder() {\n    _s();\n    var t = (0,libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"])().t;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: t(\"Start writing...\")\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 153,\n        columnNumber: 12\n    }, this);\n}\n_s(Placeholder, \"82N5KF9nLzZ6+2WH7KIjzIXRkLw=\", false, function() {\n    return [\n        libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Placeholder;\nvar LexicalEditor = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(_c1 = _s1(function(param, ref) {\n    var _readOnly = param.readOnly, readOnly = _readOnly === void 0 ? false : _readOnly, _value = param.value, value = _value === void 0 ? \"\" : _value, onChange = param.onChange, onClickLink = param.onClickLink, onHoverLink = param.onHoverLink, _className = param.className, className = _className === void 0 ? \"\" : _className;\n    _s1();\n    var _s = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n    var height = (0,react_div_100vh__WEBPACK_IMPORTED_MODULE_4__.use100vh)();\n    var mounted = (0,libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // 创建一个 ref 来存储编辑器实例\n    var editorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    var initialConfig = {\n        namespace: \"LexicalEditor\",\n        theme: theme,\n        onError: function onError(error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n            // Lexical原生的ListItemNode已经支持checkbox功能\n            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n        ],\n        editable: !readOnly,\n        // 设置初始内容 - 如果有值就解析，否则创建空状态\n        editorState: value && value.trim() ? function() {\n            try {\n                var editorStateData = JSON.parse(value);\n                var tempEditor = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.createEditor)({\n                    namespace: \"TempEditor\",\n                    nodes: [\n                        _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n                        _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n                        _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n                        _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n                        _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n                        _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n                        _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n                        _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n                        _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n                        _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n                    ],\n                    onError: function(error) {\n                        return console.error(\"Temp Editor Error:\", error);\n                    }\n                });\n                return tempEditor.parseEditorState(editorStateData);\n            } catch (error) {\n                console.error(\"Failed to parse initial editor state:\", error);\n                return null;\n            }\n        }() : null\n    };\n    // 创建自定义transformers，包含图片支持\n    var IMAGE_TRANSFORMER = {\n        dependencies: [\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode\n        ],\n        export: function(node) {\n            if (!(0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$isImageNode)(node)) {\n                return null;\n            }\n            return \"![\".concat(node.getAltText(), \"](\").concat(node.getSrc(), \")\");\n        },\n        regExp: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/,\n        replace: function(parentNode, children, match) {\n            var _match = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(match, 3), altText = _match[1], src = _match[2];\n            var imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$createImageNode)({\n                altText: altText,\n                src: src,\n                maxWidth: 800\n            });\n            children.forEach(function(child) {\n                return child.remove();\n            });\n            parentNode.append(imageNode);\n        },\n        type: \"element\"\n    };\n    // 创建自定义的下划线转换器，使用 <u>text</u> 语法\n    var UNDERLINE_TRANSFORMER = {\n        format: [\n            \"underline\"\n        ],\n        tag: \"<u>\",\n        type: \"text-format\"\n    };\n    // 移除段落缩进转换器 - 专注于列表缩进功能\n    // 段落缩进不是标准markdown语法，我们只处理列表缩进\n    // 创建水平分割线转换器\n    var HR_TRANSFORMER = {\n        dependencies: [\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode\n        ],\n        export: function(node) {\n            return (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$isHorizontalRuleNode)(node) ? \"---\" : null;\n        },\n        regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n        replace: function(parentNode, children, match, isImport) {\n            var line = (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$createHorizontalRuleNode)();\n            if (isImport || parentNode.getNextSibling() != null) {\n                parentNode.replace(line);\n            } else {\n                parentNode.insertBefore(line);\n            }\n            line.selectNext();\n        },\n        type: \"element\"\n    };\n    // 🔧 JSON格式保存 - 完全使用Lexical原生格式\n    // 使用标准的Lexical transformers\n    var customTransformers = (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__[\"default\"])(_lexical_markdown__WEBPACK_IMPORTED_MODULE_20__.TRANSFORMERS).concat([\n        // 添加我们的自定义转换器\n        HR_TRANSFORMER,\n        UNDERLINE_TRANSFORMER,\n        IMAGE_TRANSFORMER\n    ]);\n    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题\n    // 用于跟踪是否是用户输入导致的变化\n    var isUserInputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(editorState, _editor, tags) {\n        if (onChange) {\n            // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新\n            if (tags.has(\"history-merge\") || tags.has(\"content-sync\")) {\n                return;\n            }\n            // 标记这是用户输入\n            isUserInputRef.current = true;\n            editorState.read(function() {\n                try {\n                    // 使用Lexical原生JSON格式保存，完美保持嵌套关系\n                    var editorStateJSON = JSON.stringify(editorState.toJSON());\n                    // 简单的内容变化检查（比较JSON字符串）\n                    if (editorStateJSON !== value) {\n                        onChange(function() {\n                            return editorStateJSON;\n                        });\n                    }\n                } catch (error) {\n                    console.error(\"Error in handleChange:\", error);\n                // 如果转换出错，保持原有内容不变\n                }\n            });\n        }\n    }, [\n        onChange,\n        value\n    ]);\n    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑\n    var ListExitPlugin = function() {\n        _s();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            return editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_17__.KEY_ENTER_COMMAND, function(event) {\n                var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getSelection)();\n                if (!(0,lexical__WEBPACK_IMPORTED_MODULE_17__.$isRangeSelection)(selection)) {\n                    return false;\n                }\n                var anchorNode = selection.anchor.getNode();\n                // 检查是否在空的列表项中\n                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(anchorNode)) {\n                    var textContent = anchorNode.getTextContent().trim();\n                    if (textContent === \"\") {\n                        var listNode = anchorNode.getParent();\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(listNode)) {\n                            // 如果是空的列表项，退出列表\n                            event === null || event === void 0 ? void 0 : event.preventDefault();\n                            // 创建新段落并在列表后插入\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                            listNode.insertAfter(paragraph);\n                            // 删除空的列表项\n                            anchorNode.remove();\n                            // 选中新段落\n                            paragraph.select();\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            }, lexical__WEBPACK_IMPORTED_MODULE_17__.COMMAND_PRIORITY_HIGH);\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s(ListExitPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 内容同步组件 - 只在外部更新时设置内容，避免光标丢失\n    var ContentSyncPlugin = function() {\n        _s2();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        var _$ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(undefined), lastInitializedValue = _$ref1[0], setLastInitializedValue = _$ref1[1];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (editor && value !== undefined && mounted) {\n                // 只有当 value 与上次初始化的值不同，且不是用户输入导致的变化时才重新设置\n                if (value !== lastInitializedValue && !isUserInputRef.current) {\n                    var timeoutId = setTimeout(function() {\n                        if (value.trim()) {\n                            try {\n                                // 解析JSON格式的编辑器状态\n                                var editorStateData = JSON.parse(value);\n                                // 直接设置编辑器状态\n                                var newEditorState = editor.parseEditorState(editorStateData);\n                                editor.setEditorState(newEditorState);\n                            } catch (jsonError) {\n                                // JSON解析失败，创建空编辑器\n                                editor.update(function() {\n                                    var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                    root.clear();\n                                    var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                    root.append(paragraph);\n                                });\n                            }\n                        } else {\n                            // 空内容时清空并创建一个空段落\n                            editor.update(function() {\n                                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                root.clear();\n                                var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                root.append(paragraph);\n                            });\n                        }\n                        setLastInitializedValue(value);\n                    }, 0);\n                    // 清理函数\n                    return function() {\n                        clearTimeout(timeoutId);\n                    };\n                }\n                // 重置用户输入标记\n                if (isUserInputRef.current) {\n                    isUserInputRef.current = false;\n                }\n            }\n        }, [\n            editor,\n            value,\n            mounted,\n            lastInitializedValue\n        ]);\n        return null;\n    };\n    _s2(ContentSyncPlugin, \"oF58+QeaBOzsoJgaBL9rKVedP/c=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 编辑器引用组件 - 获取编辑器实例\n    var EditorRefPlugin = function() {\n        _s3();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            editorRef.current = editor;\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s3(EditorRefPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function() {\n        return {\n            focusAtEnd: function() {\n                // 实现焦点到编辑器末尾\n                if (editorRef.current) {\n                    editorRef.current.focus(function() {\n                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                        var lastChild = root.getLastChild();\n                        if (lastChild) {\n                            lastChild.selectEnd();\n                        } else {\n                            root.selectEnd();\n                        }\n                    });\n                }\n            },\n            focusAtStart: function() {\n                // 实现焦点到编辑器开始\n                if (editorRef.current) {\n                    editorRef.current.focus(function() {\n                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                        var firstChild = root.getFirstChild();\n                        if (firstChild) {\n                            firstChild.selectStart();\n                        } else {\n                            root.selectStart();\n                        }\n                    });\n                }\n            }\n        };\n    });\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"b6e8ec94f89652f1\",\n                [\n                    height ? height + \"px\" : \"100vh\"\n                ]\n            ]\n        ]) + \" \" + \"lexical-editor \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__.LexicalComposer, {\n                initialConfig: initialConfig,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"b6e8ec94f89652f1\",\n                            [\n                                height ? height + \"px\" : \"100vh\"\n                            ]\n                        ]\n                    ]) + \" \" + \"editor-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__.ContentEditable, {\n                                className: \"editor-input focus:outline-none w-full\",\n                                spellCheck: false\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 483,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__.LinkPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 485,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__.ListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 486,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__.CheckListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__.MarkdownShortcutPlugin, {\n                            transformers: customTransformers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__.HorizontalRulePlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            enabled: true,\n                            debug: \"development\" === \"development\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListExitPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditorRefPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__.OnChangePlugin, {\n                            onChange: handleChange,\n                            ignoreHistoryMergeTagChange: true,\n                            ignoreSelectionChange: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 21\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                    lineNumber: 472,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                lineNumber: 471,\n                columnNumber: 13\n            }, _this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"b6e8ec94f89652f1\",\n                dynamic: [\n                    height ? height + \"px\" : \"100vh\"\n                ],\n                children: \".lexical-editor{position:relative}.editor-container{position:relative}.editor-input{outline:none;padding:1rem 0;min-height:-webkit-calc(\".concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:-moz-calc(\").concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:calc(\").concat(height ? height + \"px\" : \"100vh\", ' - 14rem);padding-bottom:10rem;width:100%;max-width:none;line-height:1.7;font-size:1rem;color:inherit;-webkit-spellcheck:false;-moz-spellcheck:false;-ms-spellcheck:false;spellcheck:false}.lexical-editor{--lexical-indent-base-value:32px}.lexical-indent{--lexical-indent-base-value:32px}.editor-input p[style*=\"margin-left\"],.editor-input h1[style*=\"margin-left\"],.editor-input h2[style*=\"margin-left\"],.editor-input h3[style*=\"margin-left\"],.editor-input h4[style*=\"margin-left\"],.editor-input h5[style*=\"margin-left\"],.editor-input h6[style*=\"margin-left\"],.editor-input li[style*=\"margin-left\"]{-webkit-transition:margin-left.2s ease;-moz-transition:margin-left.2s ease;-o-transition:margin-left.2s ease;transition:margin-left.2s ease}.editor-placeholder{color:#999;overflow:hidden;position:absolute;-o-text-overflow:ellipsis;text-overflow:ellipsis;top:1rem;left:0;font-size:1rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;pointer-events:none}.editor-paragraph{margin:1.2rem 0;line-height:1.8}.editor-heading-h1{font-size:2.8em;font-weight:bold;margin:1.5rem 0 1rem 0;line-height:1.2}.editor-heading-h2{font-size:2.2em;font-weight:bold;margin:1.4rem 0 .8rem 0;line-height:1.3}.editor-heading-h3{font-size:1.8em;font-weight:bold;margin:1.3rem 0 .6rem 0;line-height:1.4}.editor-heading-h4{font-size:1.5em;font-weight:bold;margin:1.2rem 0 .5rem 0;line-height:1.4}.editor-heading-h5{font-size:1.3em;font-weight:bold;margin:1.1rem 0 .4rem 0;line-height:1.5}.editor-heading-h6{font-size:1.2em;font-weight:bold;margin:1rem 0 .3rem 0;line-height:1.5}.editor-quote{margin:1rem 0;padding-left:1rem;border-left:4px solid#ccc;font-style:italic;color:#666}.editor-list-ol,.editor-list-ul{margin:1rem 0;padding-left:2rem}.editor-listitem{margin:.5rem 0}.editor-link{color:#3b82f6;text-decoration:underline;cursor:pointer}.editor-link:hover{color:#1d4ed8}.editor-text-bold{font-weight:bold}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.editor-text-strikethrough{text-decoration:line-through}.editor-text-code{background-color:#e4e4e7;color:black;padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:monospace;font-size:.9em}.editor-code{background-color:#e4e4e7;color:black;border:1px solid#e9ecef;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;padding:1rem;margin:1rem 0;font-family:\"Courier New\",Courier,monospace;font-size:.9em;line-height:1.4;overflow-x:auto}[data-theme=\"dark\"] .editor-text-code{background-color:#3f3f46;color:white}[data-theme=\"dark\"] .editor-code{background-color:#3f3f46;color:white;border-color:#4b5563}.editor-list-ol{list-style-type:decimal;margin:.5rem 0;padding-left:1.5rem}.editor-list-ul{list-style-type:disc;margin:.5rem 0;padding-left:1.5rem}.editor-listitem{margin:.4rem 0;line-height:1.8}.editor-nested-listitem{margin:.3rem 0}.editor-list-ol-1{list-style-type:decimal}.editor-list-ol-2{list-style-type:lower-alpha}.editor-list-ol-3{list-style-type:lower-roman}.editor-list-ol-4{list-style-type:upper-alpha}.editor-list-ol-5{list-style-type:upper-roman}.editor-list-ul-1{list-style-type:disc}.editor-list-ul-2{list-style-type:circle}.editor-list-ul-3{list-style-type:square}.editor-list-ul-4{list-style-type:disc}.editor-list-ul-5{list-style-type:circle}.task-list{list-style:none;padding-left:0;margin:1rem 0}.task-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;margin:.5rem 0;list-style:none}.task-checkbox{margin-right:.5rem;margin-top:.125rem;cursor:pointer;width:1rem;height:1rem;border:1px solid#d1d5db;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;background:white;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-appearance:none}.task-checkbox:checked{background-color:#3b82f6;border-color:#3b82f6;color:white}.task-checkbox:checked::after{content:\"✓\";font-size:.75rem;font-weight:bold;color:white}.task-content{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:1.7}.task-item[data-checked=\"true\"] .task-content{text-decoration:line-through;opacity:.6}.lexical-highlight,mark,.editor-text-highlight{background-color:#eab834!important;color:black!important;padding:.1rem .2rem;-webkit-border-radius:.125rem;-moz-border-radius:.125rem;border-radius:.125rem}.dark .lexical-highlight,.dark mark,.dark .editor-text-highlight,[data-theme=\"dark\"] .lexical-highlight,[data-theme=\"dark\"] mark,[data-theme=\"dark\"] .editor-text-highlight,html.dark .lexical-highlight,html.dark mark,html.dark .editor-text-highlight{background-color:#3185eb!important;color:white!important}.editor-image img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}@media(prefers-color-scheme:dark){.editor-placeholder{color:#6b7280}.editor-quote{border-left-color:#4b5563;color:#9ca3af}.editor-text-code{background-color:#374151;color:#f9fafb}.editor-code{background-color:#1f2937;border-color:#374151;color:#f9fafb}}.lexical-editor ul:not([data-lexical-list-type=\"check\"]) li::marker{color:#6b7280}.lexical-editor ol li::marker{color:#6b7280}.lexical-editor .PlaygroundEditorTheme__listItemChecked,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked{position:relative;margin-left:.5em;margin-right:.5em;padding-left:1.5em;padding-right:1.5em;list-style-type:none;outline:none;display:block;min-height:1.5em}.lexical-editor .PlaygroundEditorTheme__listItemChecked>*,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked>*{margin-left:.01em}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{content:\"\";width:.9em;height:.9em;top:50%;left:0;cursor:pointer;display:block;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover;position:absolute;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.lexical-editor .PlaygroundEditorTheme__listItemChecked{text-decoration:line-through}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before{-webkit-box-shadow:0 0 0 2px#a6cdfe;-moz-box-shadow:0 0 0 2px#a6cdfe;box-shadow:0 0 0 2px#a6cdfe;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before{border:1px solid#999;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{border:1px solid rgb(61,135,245);-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;background-color:#3d87f5;background-repeat:no-repeat}.lexical-editor .PlaygroundEditorTheme__listItemChecked:after{content:\"\";cursor:pointer;border-color:#fff;border-style:solid;position:absolute;display:block;top:45%;width:.2em;left:.35em;height:.4em;-webkit-transform:translateY(-50%)rotate(45deg);-moz-transform:translateY(-50%)rotate(45deg);-ms-transform:translateY(-50%)rotate(45deg);-o-transform:translateY(-50%)rotate(45deg);transform:translateY(-50%)rotate(45deg);border-width:0 .1em .1em 0}')\n            }, void 0, false, void 0, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 470,\n        columnNumber: 9\n    }, _this);\n}, \"8I2AD+AlX6+pWFkqUp3/B9iwl/M=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n})), \"8I2AD+AlX6+pWFkqUp3/B9iwl/M=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c2 = LexicalEditor;\nLexicalEditor.displayName = \"LexicalEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Placeholder\");\n$RefreshReg$(_c1, \"LexicalEditor$forwardRef\");\n$RefreshReg$(_c2, \"LexicalEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-editor.tsx\n"));

/***/ })

});