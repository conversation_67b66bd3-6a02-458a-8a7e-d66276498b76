"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/container/lexical-edit-container.tsx":
/*!*********************************************************!*\
  !*** ./components/container/lexical-edit-container.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var components_editor_lexical_main_editor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! components/editor/lexical-main-editor */ \"./components/editor/lexical-main-editor.tsx\");\n/* harmony import */ var libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/state/lexical-editor */ \"./libs/web/state/lexical-editor.ts\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var libs_web_state_tree__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! libs/web/state/tree */ \"./libs/web/state/tree.ts\");\n/* harmony import */ var libs_web_state_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! libs/web/state/ui */ \"./libs/web/state/ui.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_hooks_use_auto_save_on_leave__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! libs/web/hooks/use-auto-save-on-leave */ \"./libs/web/hooks/use-auto-save-on-leave.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var components_note_nav__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! components/note-nav */ \"./components/note-nav.tsx\");\n/* harmony import */ var components_editor_delete_alert__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! components/editor/delete-alert */ \"./components/editor/delete-alert.tsx\");\n/**\n * Lexical Edit Container Component\n * Migrated from TipTap to Lexical\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar LexicalEditContainer = function() {\n    _s();\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    var _query = router.query, id = _query.id, pid = _query.pid;\n    var isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_3__.has)(router.query, \"new\");\n    var ref = libs_web_state_note__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useContainer(), initNote = ref.initNote, findOrCreateNote = ref.findOrCreateNote, fetchNote = ref.fetchNote;\n    var loadNoteOnDemand = libs_web_state_tree__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useContainer().loadNoteOnDemand;\n    var ref1 = libs_web_state_ui__WEBPACK_IMPORTED_MODULE_8__[\"default\"].useContainer(), settings = ref1.settings.settings;\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    (0,libs_web_hooks_use_auto_save_on_leave__WEBPACK_IMPORTED_MODULE_10__[\"default\"])({\n        enabled: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var initializeEditor = function() {\n            var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(function() {\n                var dailyDate, cachedNote, noteData, error;\n                return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            if (!id || Array.isArray(id)) return [\n                                2\n                            ];\n                            if (!isNew) return [\n                                3,\n                                4\n                            ];\n                            dailyDate = router.query.daily;\n                            if (!(dailyDate && /^\\d{4}-\\d{1,2}-\\d{1,2}$/.test(dailyDate))) return [\n                                3,\n                                1\n                            ];\n                            initNote({\n                                id: id,\n                                title: dailyDate,\n                                content: \"\\n\",\n                                pid: settings.daily_root_id,\n                                isDailyNote: true\n                            });\n                            return [\n                                3,\n                                3\n                            ];\n                        case 1:\n                            return [\n                                4,\n                                libs_web_cache_note__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getItem(id)\n                            ];\n                        case 2:\n                            cachedNote = _state.sent();\n                            if (cachedNote) {\n                                initNote(cachedNote);\n                                return [\n                                    2\n                                ];\n                            }\n                            initNote({\n                                id: id,\n                                title: \"\",\n                                content: \"\\n\",\n                                pid: (typeof pid === \"string\" ? pid : undefined) || \"root\"\n                            });\n                            _state.label = 3;\n                        case 3:\n                            return [\n                                3,\n                                10\n                            ];\n                        case 4:\n                            _state.trys.push([\n                                4,\n                                9,\n                                ,\n                                10\n                            ]);\n                            return [\n                                4,\n                                loadNoteOnDemand(id)\n                            ];\n                        case 5:\n                            noteData = _state.sent();\n                            if (!noteData) return [\n                                3,\n                                6\n                            ];\n                            initNote(noteData);\n                            return [\n                                3,\n                                8\n                            ];\n                        case 6:\n                            console.log(\"\\uD83D\\uDD04 Fallback to direct API call for note:\", id);\n                            return [\n                                4,\n                                fetchNote(id)\n                            ];\n                        case 7:\n                            _state.sent();\n                            _state.label = 8;\n                        case 8:\n                            return [\n                                3,\n                                10\n                            ];\n                        case 9:\n                            error = _state.sent();\n                            console.error(\"Failed to load note:\", error);\n                            toast(\"Failed to load note\", \"error\");\n                            return [\n                                3,\n                                10\n                            ];\n                        case 10:\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function initializeEditor() {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        initializeEditor();\n    }, [\n        id,\n        isNew,\n        pid,\n        router.query.daily,\n        settings.daily_root_id,\n        initNote,\n        findOrCreateNote,\n        fetchNote,\n        loadNoteOnDemand,\n        toast, \n    ]);\n    if (!id || Array.isArray(id)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\container\\\\lexical-edit-container.tsx\",\n            lineNumber: 109,\n            columnNumber: 16\n        }, _this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Provider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_note_nav__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\container\\\\lexical-edit-container.tsx\",\n                lineNumber: 114,\n                columnNumber: 13\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_editor_delete_alert__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\container\\\\lexical-edit-container.tsx\",\n                lineNumber: 115,\n                columnNumber: 13\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_editor_lexical_main_editor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    readOnly: false\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\container\\\\lexical-edit-container.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\container\\\\lexical-edit-container.tsx\",\n                lineNumber: 116,\n                columnNumber: 13\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\container\\\\lexical-edit-container.tsx\",\n        lineNumber: 113,\n        columnNumber: 9\n    }, _this);\n};\n_s(LexicalEditContainer, \"uLhHtFDRcKLsyU7rXN+LxiiX+GY=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        libs_web_state_note__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useContainer,\n        libs_web_state_tree__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useContainer,\n        libs_web_state_ui__WEBPACK_IMPORTED_MODULE_8__[\"default\"].useContainer,\n        libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast,\n        libs_web_hooks_use_auto_save_on_leave__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    ];\n});\n_c = LexicalEditContainer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditContainer);\nvar _c;\n$RefreshReg$(_c, \"LexicalEditContainer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2NvbnRhaW5lci9sZXhpY2FsLWVkaXQtY29udGFpbmVyLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTs7Ozs7Ozs7Ozs7Ozs7OztDQWdCQyxHQUVEOzs7OztBQUFzQztBQUNFO0FBQ1g7QUFDeUM7QUFDUDtBQUNuQjtBQUNJO0FBQ1I7QUFDWTtBQUNtQjtBQUMzQjtBQUNGO0FBQ2U7QUFFekQsSUFBTWEsb0JBQW9CLEdBQU8sV0FBTTs7SUFDbkMsSUFBTUMsTUFBTSxHQUFHYixzREFBUyxFQUFFO0lBQzFCLElBQW9CYSxNQUFZLEdBQVpBLE1BQU0sQ0FBQ0MsS0FBSyxFQUF4QkMsRUFBRSxHQUFVRixNQUFZLENBQXhCRSxFQUFFLEVBQUVDLEdBQUcsR0FBS0gsTUFBWSxDQUFwQkcsR0FBRztJQUNmLElBQU1DLEtBQUssR0FBR2hCLDJDQUFHLENBQUNZLE1BQU0sQ0FBQ0MsS0FBSyxFQUFFLEtBQUssQ0FBQztJQUN0QyxJQUFrRFYsR0FBd0IsR0FBeEJBLHdFQUFzQixFQUFFLEVBQWxFZSxRQUFRLEdBQWtDZixHQUF3QixDQUFsRWUsUUFBUSxFQUFFQyxnQkFBZ0IsR0FBZ0JoQixHQUF3QixDQUF4RGdCLGdCQUFnQixFQUFFQyxTQUFTLEdBQUtqQixHQUF3QixDQUF0Q2lCLFNBQVM7SUFDN0MsSUFBTSxnQkFBa0IsR0FBS2hCLHdFQUEwQixFQUFFLENBQWpEaUIsZ0JBQWdCO0lBQ3hCLElBRUloQixJQUFzQixHQUF0QkEsc0VBQW9CLEVBQUUsRUFEdEJpQixRQUFvQixHQUNwQmpCLElBQXNCLENBRHRCaUIsUUFBUSxDQUFJQSxRQUFRO0lBRXhCLElBQU1DLEtBQUssR0FBR2pCLGtFQUFRLEVBQUU7SUFFeEJDLGtGQUFrQixDQUFDO1FBQ2ZpQixPQUFPLEVBQUUsSUFBSTtLQUNoQixDQUFDLENBQUM7SUFFSDFCLGdEQUFTLENBQUMsV0FBTTtRQUNaLElBQU0yQixnQkFBZ0I7dUJBQUcsZ0dBQVk7b0JBSXZCQyxTQUFTLEVBV0xDLFVBQVUsRUFlVkMsUUFBUSxFQVFUQyxLQUFLOzs7OzRCQXJDbEIsSUFBSSxDQUFDZixFQUFFLElBQUlnQixLQUFLLENBQUNDLE9BQU8sQ0FBQ2pCLEVBQUUsQ0FBQyxFQUFFOzs4QkFBTztpQ0FFakNFLEtBQUssRUFBTEE7Ozs4QkFBSzs0QkFDQ1UsU0FBUyxHQUFHZCxNQUFNLENBQUNDLEtBQUssQ0FBQ21CLEtBQUssQ0FBVztpQ0FFM0NOLENBQUFBLFNBQVMsSUFBSSwwQkFBMEJPLElBQUksQ0FBQ1AsU0FBUyxDQUFDLEdBQXREQTs7OzhCQUFzRDs0QkFDdERSLFFBQVEsQ0FBQztnQ0FDTEosRUFBRSxFQUFGQSxFQUFFO2dDQUNGb0IsS0FBSyxFQUFFUixTQUFTO2dDQUNoQlMsT0FBTyxFQUFFLElBQUk7Z0NBQ2JwQixHQUFHLEVBQUVPLFFBQVEsQ0FBQ2MsYUFBYTtnQ0FDM0JDLFdBQVcsRUFBRSxJQUFJOzZCQUNwQixDQUFDLENBQUM7Ozs7Ozs0QkFFZ0I7O2dDQUFNN0Isb0VBQWlCLENBQUNNLEVBQUUsQ0FBQzs4QkFBQTs7NEJBQXhDYSxVQUFVLEdBQUcsYUFBMkI7NEJBQzlDLElBQUlBLFVBQVUsRUFBRTtnQ0FDWlQsUUFBUSxDQUFDUyxVQUFVLENBQUMsQ0FBQztnQ0FDckI7O2tDQUFPOzRCQUNYLENBQUM7NEJBRURULFFBQVEsQ0FBQztnQ0FDTEosRUFBRSxFQUFGQSxFQUFFO2dDQUNGb0IsS0FBSyxFQUFFLEVBQUU7Z0NBQ1RDLE9BQU8sRUFBRSxJQUFJO2dDQUNicEIsR0FBRyxFQUFFLENBQUMsT0FBT0EsR0FBRyxLQUFLLFFBQVEsR0FBR0EsR0FBRyxHQUFHd0IsU0FBUyxLQUFLLE1BQU07NkJBQzdELENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7NEJBSWM7O2dDQUFNbEIsZ0JBQWdCLENBQUNQLEVBQUUsQ0FBQzs4QkFBQTs7NEJBQXJDYyxRQUFRLEdBQUcsYUFBMEI7aUNBRXZDQSxRQUFRLEVBQVJBOzs7OEJBQVE7NEJBQ1JWLFFBQVEsQ0FBQ1UsUUFBUSxDQUFDLENBQUM7Ozs7Ozs0QkFFbkJZLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG9EQUF5QyxFQUFFM0IsRUFBRSxDQUFDLENBQUM7NEJBQzNEOztnQ0FBTU0sU0FBUyxDQUFDTixFQUFFLENBQUM7OEJBQUE7OzRCQUFuQixhQUFtQixDQUFDOzs7Ozs7Ozs0QkFFbkJlLEtBQUs7NEJBQ1ZXLE9BQU8sQ0FBQ1gsS0FBSyxDQUFDLHNCQUFzQixFQUFFQSxLQUFLLENBQUMsQ0FBQzs0QkFDN0NOLEtBQUssQ0FBQyxxQkFBcUIsRUFBRSxPQUFPLENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7WUFHbEQsQ0FBQzs0QkEzQ0tFLGdCQUFnQjs7O1dBMkNyQjtRQUVEQSxnQkFBZ0IsRUFBRSxDQUFDO0lBQ3ZCLENBQUMsRUFBRTtRQUNDWCxFQUFFO1FBQ0ZFLEtBQUs7UUFDTEQsR0FBRztRQUNISCxNQUFNLENBQUNDLEtBQUssQ0FBQ21CLEtBQUs7UUFDbEJWLFFBQVEsQ0FBQ2MsYUFBYTtRQUN0QmxCLFFBQVE7UUFDUkMsZ0JBQWdCO1FBQ2hCQyxTQUFTO1FBQ1RDLGdCQUFnQjtRQUNoQkUsS0FBSztLQUNSLENBQUMsQ0FBQztJQUVILElBQUksQ0FBQ1QsRUFBRSxJQUFJZ0IsS0FBSyxDQUFDQyxPQUFPLENBQUNqQixFQUFFLENBQUMsRUFBRTtRQUMxQixxQkFBTyw4REFBQzRCLEtBQUc7c0JBQUMsWUFBVTs7Ozs7aUJBQU0sQ0FBQztJQUNqQyxDQUFDO0lBRUQscUJBQ0ksOERBQUN4Qyw4RUFBMkI7OzBCQUN4Qiw4REFBQ08sNERBQU87Ozs7cUJBQUc7MEJBQ1gsOERBQUNDLHVFQUFXOzs7O3FCQUFHOzBCQUNmLDhEQUFDa0MsU0FBTztnQkFBQ0MsU0FBUyxFQUFDLFFBQVE7MEJBQ3ZCLDRFQUFDNUMsNkVBQWlCO29CQUFDNkMsUUFBUSxFQUFFLEtBQUs7Ozs7O3lCQUFJOzs7OztxQkFDaEM7Ozs7OzthQUNnQixDQUNoQztBQUNOLENBQUM7R0F4RktuQyxvQkFBb0I7O1FBQ1BaLGtEQUFTO1FBRzBCSSx3RUFBc0I7UUFDM0NDLHdFQUEwQjtRQUduREMsc0VBQW9CO1FBQ1ZDLDhEQUFRO1FBRXRCQyw4RUFBa0I7OztBQVhoQkksS0FBQUEsb0JBQW9CO0FBMEYxQiwrREFBZUEsb0JBQW9CLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9jb250YWluZXIvbGV4aWNhbC1lZGl0LWNvbnRhaW5lci50c3g/NTNiZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIExleGljYWwgRWRpdCBDb250YWluZXIgQ29tcG9uZW50XG4gKiBNaWdyYXRlZCBmcm9tIFRpcFRhcCB0byBMZXhpY2FsXG4gKlxuICogQ29weXJpZ2h0IChjKSAyMDI1IHdheWNhYW5cbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgTGljZW5zZVxuICpcbiAqIFBlcm1pc3Npb24gaXMgaGVyZWJ5IGdyYW50ZWQsIGZyZWUgb2YgY2hhcmdlLCB0byBhbnkgcGVyc29uIG9idGFpbmluZyBhIGNvcHlcbiAqIG9mIHRoaXMgc29mdHdhcmUgYW5kIGFzc29jaWF0ZWQgZG9jdW1lbnRhdGlvbiBmaWxlcyAodGhlIFwiU29mdHdhcmVcIiksIHRvIGRlYWxcbiAqIGluIHRoZSBTb2Z0d2FyZSB3aXRob3V0IHJlc3RyaWN0aW9uLCBpbmNsdWRpbmcgd2l0aG91dCBsaW1pdGF0aW9uIHRoZSByaWdodHNcbiAqIHRvIHVzZSwgY29weSwgbW9kaWZ5LCBtZXJnZSwgcHVibGlzaCwgZGlzdHJpYnV0ZSwgc3VibGljZW5zZSwgYW5kL29yIHNlbGxcbiAqIGNvcGllcyBvZiB0aGUgU29mdHdhcmUsIGFuZCB0byBwZXJtaXQgcGVyc29ucyB0byB3aG9tIHRoZSBTb2Z0d2FyZSBpc1xuICogZnVybmlzaGVkIHRvIGRvIHNvLCBzdWJqZWN0IHRvIHRoZSBmb2xsb3dpbmcgY29uZGl0aW9uczpcbiAqXG4gKiBUaGUgYWJvdmUgY29weXJpZ2h0IG5vdGljZSBhbmQgdGhpcyBwZXJtaXNzaW9uIG5vdGljZSBzaGFsbCBiZSBpbmNsdWRlZCBpbiBhbGxcbiAqIGNvcGllcyBvciBzdWJzdGFudGlhbCBwb3J0aW9ucyBvZiB0aGUgU29mdHdhcmUuXG4gKi9cblxuaW1wb3J0IHsgRkMsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvcm91dGVyJztcbmltcG9ydCB7IGhhcyB9IGZyb20gJ2xvZGFzaCc7XG5pbXBvcnQgTGV4aWNhbE1haW5FZGl0b3IgZnJvbSAnY29tcG9uZW50cy9lZGl0b3IvbGV4aWNhbC1tYWluLWVkaXRvcic7XG5pbXBvcnQgTGV4aWNhbEVkaXRvclN0YXRlIGZyb20gJ2xpYnMvd2ViL3N0YXRlL2xleGljYWwtZWRpdG9yJztcbmltcG9ydCBOb3RlU3RhdGUgZnJvbSAnbGlicy93ZWIvc3RhdGUvbm90ZSc7XG5pbXBvcnQgTm90ZVRyZWVTdGF0ZSBmcm9tICdsaWJzL3dlYi9zdGF0ZS90cmVlJztcbmltcG9ydCBVSVN0YXRlIGZyb20gJ2xpYnMvd2ViL3N0YXRlL3VpJztcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnbGlicy93ZWIvaG9va3MvdXNlLXRvYXN0JztcbmltcG9ydCB1c2VBdXRvU2F2ZU9uTGVhdmUgZnJvbSAnbGlicy93ZWIvaG9va3MvdXNlLWF1dG8tc2F2ZS1vbi1sZWF2ZSc7XG5pbXBvcnQgbm90ZUNhY2hlIGZyb20gJ2xpYnMvd2ViL2NhY2hlL25vdGUnO1xuaW1wb3J0IE5vdGVOYXYgZnJvbSAnY29tcG9uZW50cy9ub3RlLW5hdic7XG5pbXBvcnQgRGVsZXRlQWxlcnQgZnJvbSAnY29tcG9uZW50cy9lZGl0b3IvZGVsZXRlLWFsZXJ0JztcblxuY29uc3QgTGV4aWNhbEVkaXRDb250YWluZXI6IEZDID0gKCkgPT4ge1xuICAgIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICAgIGNvbnN0IHsgaWQsIHBpZCB9ID0gcm91dGVyLnF1ZXJ5O1xuICAgIGNvbnN0IGlzTmV3ID0gaGFzKHJvdXRlci5xdWVyeSwgJ25ldycpO1xuICAgIGNvbnN0IHsgaW5pdE5vdGUsIGZpbmRPckNyZWF0ZU5vdGUsIGZldGNoTm90ZSB9ID0gTm90ZVN0YXRlLnVzZUNvbnRhaW5lcigpO1xuICAgIGNvbnN0IHsgbG9hZE5vdGVPbkRlbWFuZCB9ID0gTm90ZVRyZWVTdGF0ZS51c2VDb250YWluZXIoKTtcbiAgICBjb25zdCB7XG4gICAgICAgIHNldHRpbmdzOiB7IHNldHRpbmdzIH0sXG4gICAgfSA9IFVJU3RhdGUudXNlQ29udGFpbmVyKCk7XG4gICAgY29uc3QgdG9hc3QgPSB1c2VUb2FzdCgpO1xuXG4gICAgdXNlQXV0b1NhdmVPbkxlYXZlKHtcbiAgICAgICAgZW5hYmxlZDogdHJ1ZSxcbiAgICB9KTtcblxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGNvbnN0IGluaXRpYWxpemVFZGl0b3IgPSBhc3luYyAoKSA9PiB7XG4gICAgICAgICAgICBpZiAoIWlkIHx8IEFycmF5LmlzQXJyYXkoaWQpKSByZXR1cm47XG5cbiAgICAgICAgICAgIGlmIChpc05ldykge1xuICAgICAgICAgICAgICAgIGNvbnN0IGRhaWx5RGF0ZSA9IHJvdXRlci5xdWVyeS5kYWlseSBhcyBzdHJpbmc7XG5cbiAgICAgICAgICAgICAgICBpZiAoZGFpbHlEYXRlICYmIC9eXFxkezR9LVxcZHsxLDJ9LVxcZHsxLDJ9JC8udGVzdChkYWlseURhdGUpKSB7XG4gICAgICAgICAgICAgICAgICAgIGluaXROb3RlKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkLFxuICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IGRhaWx5RGF0ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICdcXG4nLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGlkOiBzZXR0aW5ncy5kYWlseV9yb290X2lkLFxuICAgICAgICAgICAgICAgICAgICAgICAgaXNEYWlseU5vdGU6IHRydWUsIFxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjYWNoZWROb3RlID0gYXdhaXQgbm90ZUNhY2hlLmdldEl0ZW0oaWQpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoY2FjaGVkTm90ZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaW5pdE5vdGUoY2FjaGVkTm90ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgICBpbml0Tm90ZSh7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAnJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICdcXG4nLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGlkOiAodHlwZW9mIHBpZCA9PT0gJ3N0cmluZycgPyBwaWQgOiB1bmRlZmluZWQpIHx8ICdyb290J1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5vdGVEYXRhID0gYXdhaXQgbG9hZE5vdGVPbkRlbWFuZChpZCk7XG5cbiAgICAgICAgICAgICAgICAgICAgaWYgKG5vdGVEYXRhKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpbml0Tm90ZShub3RlRGF0YSk7XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBGYWxsYmFjayB0byBkaXJlY3QgQVBJIGNhbGwgZm9yIG5vdGU6JywgaWQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgZmV0Y2hOb3RlKGlkKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIG5vdGU6JywgZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICB0b2FzdCgnRmFpbGVkIHRvIGxvYWQgbm90ZScsICdlcnJvcicpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcblxuICAgICAgICBpbml0aWFsaXplRWRpdG9yKCk7XG4gICAgfSwgW1xuICAgICAgICBpZCxcbiAgICAgICAgaXNOZXcsXG4gICAgICAgIHBpZCxcbiAgICAgICAgcm91dGVyLnF1ZXJ5LmRhaWx5LFxuICAgICAgICBzZXR0aW5ncy5kYWlseV9yb290X2lkLFxuICAgICAgICBpbml0Tm90ZSxcbiAgICAgICAgZmluZE9yQ3JlYXRlTm90ZSxcbiAgICAgICAgZmV0Y2hOb3RlLFxuICAgICAgICBsb2FkTm90ZU9uRGVtYW5kLFxuICAgICAgICB0b2FzdCxcbiAgICBdKTtcblxuICAgIGlmICghaWQgfHwgQXJyYXkuaXNBcnJheShpZCkpIHtcbiAgICAgICAgcmV0dXJuIDxkaXY+TG9hZGluZy4uLjwvZGl2PjtcbiAgICB9XG5cbiAgICByZXR1cm4gKFxuICAgICAgICA8TGV4aWNhbEVkaXRvclN0YXRlLlByb3ZpZGVyPlxuICAgICAgICAgICAgPE5vdGVOYXYgLz5cbiAgICAgICAgICAgIDxEZWxldGVBbGVydCAvPlxuICAgICAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwiaC1mdWxsXCI+XG4gICAgICAgICAgICAgICAgPExleGljYWxNYWluRWRpdG9yIHJlYWRPbmx5PXtmYWxzZX0gLz5cbiAgICAgICAgICAgIDwvc2VjdGlvbj5cbiAgICAgICAgPC9MZXhpY2FsRWRpdG9yU3RhdGUuUHJvdmlkZXI+XG4gICAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IExleGljYWxFZGl0Q29udGFpbmVyO1xuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVJvdXRlciIsImhhcyIsIkxleGljYWxNYWluRWRpdG9yIiwiTGV4aWNhbEVkaXRvclN0YXRlIiwiTm90ZVN0YXRlIiwiTm90ZVRyZWVTdGF0ZSIsIlVJU3RhdGUiLCJ1c2VUb2FzdCIsInVzZUF1dG9TYXZlT25MZWF2ZSIsIm5vdGVDYWNoZSIsIk5vdGVOYXYiLCJEZWxldGVBbGVydCIsIkxleGljYWxFZGl0Q29udGFpbmVyIiwicm91dGVyIiwicXVlcnkiLCJpZCIsInBpZCIsImlzTmV3IiwidXNlQ29udGFpbmVyIiwiaW5pdE5vdGUiLCJmaW5kT3JDcmVhdGVOb3RlIiwiZmV0Y2hOb3RlIiwibG9hZE5vdGVPbkRlbWFuZCIsInNldHRpbmdzIiwidG9hc3QiLCJlbmFibGVkIiwiaW5pdGlhbGl6ZUVkaXRvciIsImRhaWx5RGF0ZSIsImNhY2hlZE5vdGUiLCJub3RlRGF0YSIsImVycm9yIiwiQXJyYXkiLCJpc0FycmF5IiwiZGFpbHkiLCJ0ZXN0IiwidGl0bGUiLCJjb250ZW50IiwiZGFpbHlfcm9vdF9pZCIsImlzRGFpbHlOb3RlIiwiZ2V0SXRlbSIsInVuZGVmaW5lZCIsImNvbnNvbGUiLCJsb2ciLCJkaXYiLCJQcm92aWRlciIsInNlY3Rpb24iLCJjbGFzc05hbWUiLCJyZWFkT25seSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/container/lexical-edit-container.tsx\n"));

/***/ }),

/***/ "./components/editor/lexical-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/lexical-editor.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"./node_modules/@lexical/react/LexicalComposer.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"./node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"./node_modules/@lexical/react/LexicalContentEditable.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"./node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"./node_modules/@lexical/react/LexicalAutoFocusPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"./node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @lexical/react/LexicalLinkPlugin */ \"./node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @lexical/react/LexicalListPlugin */ \"./node_modules/@lexical/react/LexicalListPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @lexical/react/LexicalMarkdownShortcutPlugin */ \"./node_modules/@lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs\");\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @lexical/markdown */ \"./node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"./node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/rich-text */ \"./node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/code */ \"./node_modules/@lexical/code/LexicalCode.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-div-100vh */ \"./node_modules/react-div-100vh/dist/esm/index.js\");\n/* harmony import */ var libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/hooks/use-mounted */ \"./libs/web/hooks/use-mounted.ts\");\n/* harmony import */ var libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/hooks/use-i18n */ \"./libs/web/hooks/use-i18n.tsx\");\n/* harmony import */ var _plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plugins/slash-commands-plugin */ \"./components/editor/plugins/slash-commands-plugin.tsx\");\n/* harmony import */ var _plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./plugins/floating-toolbar-plugin */ \"./components/editor/plugins/floating-toolbar-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @lexical/react/LexicalCheckListPlugin */ \"./node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs\");\n/* harmony import */ var _plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./plugins/highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var _plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./plugins/image-plugin */ \"./components/editor/plugins/image-plugin.tsx\");\n/* harmony import */ var _plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./plugins/ime-plugin */ \"./components/editor/plugins/ime-plugin.tsx\");\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"./node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRulePlugin */ \"./node_modules/@lexical/react/LexicalHorizontalRulePlugin.dev.mjs\");\n/**\n * Lexical Editor Component\n * Migrated from TipTap to Lexical for better performance and modern architecture\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import custom plugins and nodes\n\n\n\n\n\n\n\n\n\nvar theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\",\n    quote: \"editor-quote\",\n    heading: {\n        h1: \"editor-heading-h1\",\n        h2: \"editor-heading-h2\",\n        h3: \"editor-heading-h3\",\n        h4: \"editor-heading-h4\",\n        h5: \"editor-heading-h5\",\n        h6: \"editor-heading-h6\"\n    },\n    list: {\n        nested: {\n            listitem: \"editor-nested-listitem\"\n        },\n        ol: \"editor-list-ol\",\n        ul: \"editor-list-ul\",\n        listitem: \"editor-listitem\",\n        // 按深度定义不同的列表样式 - 这是关键！\n        olDepth: [\n            \"editor-list-ol-1\",\n            \"editor-list-ol-2\",\n            \"editor-list-ol-3\",\n            \"editor-list-ol-4\",\n            \"editor-list-ol-5\" // I, II, III...\n        ],\n        ulDepth: [\n            \"editor-list-ul-1\",\n            \"editor-list-ul-2\",\n            \"editor-list-ul-3\",\n            \"editor-list-ul-4\",\n            \"editor-list-ul-5\" // ‣\n        ],\n        checklist: \"PlaygroundEditorTheme__checklist\",\n        listitemChecked: \"PlaygroundEditorTheme__listItemChecked\",\n        listitemUnchecked: \"PlaygroundEditorTheme__listItemUnchecked\"\n    },\n    // Lexical 0.32.1 原生缩进支持\n    indent: \"lexical-indent\",\n    image: \"editor-image\",\n    link: \"editor-link\",\n    text: {\n        bold: \"editor-text-bold\",\n        italic: \"editor-text-italic\",\n        overflowed: \"editor-text-overflowed\",\n        hashtag: \"editor-text-hashtag\",\n        underline: \"editor-text-underline\",\n        strikethrough: \"editor-text-strikethrough\",\n        underlineStrikethrough: \"editor-text-underlineStrikethrough\",\n        code: \"editor-text-code\",\n        highlight: \"editor-text-highlight\"\n    },\n    code: \"editor-code\",\n    codeHighlight: {\n        atrule: \"editor-tokenAttr\",\n        attr: \"editor-tokenAttr\",\n        boolean: \"editor-tokenProperty\",\n        builtin: \"editor-tokenSelector\",\n        cdata: \"editor-tokenComment\",\n        char: \"editor-tokenSelector\",\n        class: \"editor-tokenFunction\",\n        \"class-name\": \"editor-tokenFunction\",\n        comment: \"editor-tokenComment\",\n        constant: \"editor-tokenProperty\",\n        deleted: \"editor-tokenProperty\",\n        doctype: \"editor-tokenComment\",\n        entity: \"editor-tokenOperator\",\n        function: \"editor-tokenFunction\",\n        important: \"editor-tokenVariable\",\n        inserted: \"editor-tokenSelector\",\n        keyword: \"editor-tokenAttr\",\n        namespace: \"editor-tokenVariable\",\n        number: \"editor-tokenProperty\",\n        operator: \"editor-tokenOperator\",\n        prolog: \"editor-tokenComment\",\n        property: \"editor-tokenProperty\",\n        punctuation: \"editor-tokenPunctuation\",\n        regex: \"editor-tokenVariable\",\n        selector: \"editor-tokenSelector\",\n        string: \"editor-tokenSelector\",\n        symbol: \"editor-tokenProperty\",\n        tag: \"editor-tokenProperty\",\n        url: \"editor-tokenOperator\",\n        variable: \"editor-tokenVariable\"\n    }\n};\nfunction Placeholder() {\n    _s();\n    var t = (0,libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"])().t;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: t(\"Start writing...\")\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 153,\n        columnNumber: 12\n    }, this);\n}\n_s(Placeholder, \"82N5KF9nLzZ6+2WH7KIjzIXRkLw=\", false, function() {\n    return [\n        libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Placeholder;\nvar LexicalEditor = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(_c1 = _s1(function(param, ref) {\n    var _readOnly = param.readOnly, readOnly = _readOnly === void 0 ? false : _readOnly, _value = param.value, value = _value === void 0 ? \"\" : _value, onChange = param.onChange, onClickLink = param.onClickLink, onHoverLink = param.onHoverLink, _className = param.className, className = _className === void 0 ? \"\" : _className;\n    _s1();\n    var _s = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n    var height = (0,react_div_100vh__WEBPACK_IMPORTED_MODULE_4__.use100vh)();\n    var mounted = (0,libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    var initialConfig = {\n        namespace: \"LexicalEditor\",\n        theme: theme,\n        onError: function onError(error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n            // Lexical原生的ListItemNode已经支持checkbox功能\n            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n        ],\n        editable: !readOnly,\n        // 🔧 修复：创建有效的初始编辑器状态\n        editorState: function() {\n            var editor = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.createEditor)({\n                namespace: \"LexicalEditor\",\n                nodes: [\n                    _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n                    _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n                    _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n                    _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n                    _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n                    _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n                    _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n                    _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n                    _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n                    _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n                ],\n                onError: function(error) {\n                    return console.error(\"Lexical Error:\", error);\n                }\n            });\n            // 创建基本的编辑器状态\n            return editor.parseEditorState({\n                root: {\n                    children: [\n                        {\n                            children: [],\n                            direction: null,\n                            format: \"\",\n                            indent: 0,\n                            type: \"paragraph\",\n                            version: 1\n                        }, \n                    ],\n                    direction: null,\n                    format: \"\",\n                    indent: 0,\n                    type: \"root\",\n                    version: 1\n                }\n            });\n        }\n    };\n    // 创建自定义transformers，包含图片支持\n    var IMAGE_TRANSFORMER = {\n        dependencies: [\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode\n        ],\n        export: function(node) {\n            if (!(0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$isImageNode)(node)) {\n                return null;\n            }\n            return \"![\".concat(node.getAltText(), \"](\").concat(node.getSrc(), \")\");\n        },\n        regExp: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/,\n        replace: function(parentNode, children, match) {\n            var _match = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(match, 3), altText = _match[1], src = _match[2];\n            var imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$createImageNode)({\n                altText: altText,\n                src: src,\n                maxWidth: 800\n            });\n            children.forEach(function(child) {\n                return child.remove();\n            });\n            parentNode.append(imageNode);\n        },\n        type: \"element\"\n    };\n    // 创建自定义的下划线转换器，使用 <u>text</u> 语法\n    var UNDERLINE_TRANSFORMER = {\n        format: [\n            \"underline\"\n        ],\n        tag: \"<u>\",\n        type: \"text-format\"\n    };\n    // 移除段落缩进转换器 - 专注于列表缩进功能\n    // 段落缩进不是标准markdown语法，我们只处理列表缩进\n    // 创建水平分割线转换器\n    var HR_TRANSFORMER = {\n        dependencies: [\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode\n        ],\n        export: function(node) {\n            return (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$isHorizontalRuleNode)(node) ? \"---\" : null;\n        },\n        regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n        replace: function(parentNode, children, match, isImport) {\n            var line = (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$createHorizontalRuleNode)();\n            if (isImport || parentNode.getNextSibling() != null) {\n                parentNode.replace(line);\n            } else {\n                parentNode.insertBefore(line);\n            }\n            line.selectNext();\n        },\n        type: \"element\"\n    };\n    // 🔧 JSON格式保存 - 完全使用Lexical原生格式\n    // 使用标准的Lexical transformers\n    var customTransformers = (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_19__[\"default\"])(_lexical_markdown__WEBPACK_IMPORTED_MODULE_20__.TRANSFORMERS).concat([\n        // 添加我们的自定义转换器\n        HR_TRANSFORMER,\n        UNDERLINE_TRANSFORMER,\n        IMAGE_TRANSFORMER\n    ]);\n    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题\n    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(editorState, _editor, tags) {\n        console.log(\"\\uD83D\\uDEA8 handleChange被触发!\", {\n            hasOnChange: !!onChange,\n            tags: Array.from(tags),\n            editorStateExists: !!editorState,\n            timestamp: new Date().toISOString()\n        });\n        if (!onChange) {\n            console.log(\"❌ onChange函数不存在，无法处理编辑器变化\");\n            return;\n        }\n        // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新\n        if (tags.has(\"history-merge\") || tags.has(\"content-sync\")) {\n            console.log(\"⏭️ 跳过历史合并或内容同步触发的更新\");\n            return;\n        }\n        console.log(\"✅ 开始处理编辑器状态变化\");\n        editorState.read(function() {\n            try {\n                // 🔧 添加调试：检查编辑器状态中的列表结构\n                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                var children = root.getChildren();\n                console.log(\"\\uD83D\\uDD27 编辑器根节点子元素数量:\", children.length);\n                children.forEach(function(child, index) {\n                    console.log(\"\\uD83D\\uDD27 第\".concat(index, \"个子元素:\"), {\n                        type: child.getType(),\n                        textContent: child.getTextContent()\n                    });\n                    if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(child)) {\n                        console.log(\"\\uD83D\\uDD27 第\".concat(index, \"个子元素是列表:\"), {\n                            type: child.getListType(),\n                            childrenCount: child.getChildren().length\n                        });\n                        var listItems = child.getChildren();\n                        listItems.forEach(function(item, itemIndex) {\n                            if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(item)) {\n                                var itemChildren = item.getChildren();\n                                console.log(\"\\uD83D\\uDD27   列表项\".concat(itemIndex, \":\"), {\n                                    childrenCount: itemChildren.length,\n                                    textContent: item.getTextContent(),\n                                    hasNestedList: itemChildren.some(function(c) {\n                                        return (0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(c);\n                                    })\n                                });\n                            }\n                        });\n                    }\n                });\n                // 🔧 使用Lexical原生JSON格式保存，完美保持嵌套关系\n                var editorStateJSON = JSON.stringify(editorState.toJSON());\n                console.log(\"\\uD83D\\uDD27 准备调用onChange，JSON长度:\", editorStateJSON.length);\n                // 🔧 修复：直接调用onChange，让状态管理层处理对比逻辑\n                onChange(function() {\n                    console.log(\"\\uD83D\\uDD27 onChange回调被执行，返回JSON内容\");\n                    return editorStateJSON;\n                });\n                console.log(\"✅ onChange调用完成\");\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D Error in handleChange:\", error);\n            // 如果转换出错，保持原有内容不变\n            }\n        });\n    }, [\n        onChange,\n        value\n    ]);\n    // 调试插件 - 检查编辑器状态\n    var DebugPlugin = function() {\n        _s();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器初始化完成\", {\n                isEditable: editor.isEditable(),\n                hasRootElement: !!editor.getRootElement(),\n                timestamp: new Date().toISOString()\n            });\n            // 监听所有编辑器更新\n            var removeUpdateListener = editor.registerUpdateListener(function(param) {\n                var editorState = param.editorState, prevEditorState = param.prevEditorState, tags = param.tags;\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器更新\", {\n                    tags: Array.from(tags),\n                    hasChanges: editorState !== prevEditorState,\n                    timestamp: new Date().toISOString()\n                });\n            });\n            // 监听编辑器状态变化\n            var removeEditableListener = editor.registerEditableListener(function(editable) {\n                console.log(\"\\uD83D\\uDD27 DebugPlugin: 编辑器可编辑状态变化\", {\n                    editable: editable\n                });\n            });\n            return function() {\n                removeUpdateListener();\n                removeEditableListener();\n            };\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s(DebugPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑\n    var ListExitPlugin = function() {\n        _s2();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            return editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_17__.KEY_ENTER_COMMAND, function(event) {\n                console.log(\"\\uD83D\\uDD27 ListExitPlugin: Enter键被按下\");\n                var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getSelection)();\n                if (!(0,lexical__WEBPACK_IMPORTED_MODULE_17__.$isRangeSelection)(selection)) {\n                    return false;\n                }\n                var anchorNode = selection.anchor.getNode();\n                // 检查是否在空的列表项中\n                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(anchorNode)) {\n                    var textContent = anchorNode.getTextContent().trim();\n                    if (textContent === \"\") {\n                        var listNode = anchorNode.getParent();\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(listNode)) {\n                            // 如果是空的列表项，退出列表\n                            event === null || event === void 0 ? void 0 : event.preventDefault();\n                            // 创建新段落并在列表后插入\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                            listNode.insertAfter(paragraph);\n                            // 删除空的列表项\n                            anchorNode.remove();\n                            // 选中新段落\n                            paragraph.select();\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            }, lexical__WEBPACK_IMPORTED_MODULE_17__.COMMAND_PRIORITY_HIGH);\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s2(ListExitPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 内容同步组件 - 模仿TipTap的方式\n    var ContentSyncPlugin = function() {\n        _s3();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (editor && value !== undefined && mounted) {\n                // 使用setTimeout来避免在渲染过程中调用flushSync\n                setTimeout(function() {\n                    if (value.trim()) {\n                        try {\n                            // 🔧 解析JSON格式的编辑器状态\n                            var editorStateData = JSON.parse(value);\n                            console.log(\"\\uD83D\\uDD27 加载Lexical JSON格式内容\");\n                            // 直接设置编辑器状态\n                            var newEditorState = editor.parseEditorState(editorStateData);\n                            editor.setEditorState(newEditorState);\n                        } catch (jsonError) {\n                            console.error(\"\\uD83D\\uDD27 JSON解析失败，创建空编辑器:\", jsonError);\n                            // 创建空段落\n                            editor.update(function() {\n                                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                                root.clear();\n                                var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                                root.append(paragraph);\n                            });\n                        }\n                    } else {\n                        // 空内容时清空并创建一个空段落\n                        editor.update(function() {\n                            var root = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$getRoot)();\n                            root.clear();\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_17__.$createParagraphNode)();\n                            root.append(paragraph);\n                        });\n                    }\n                }, 0);\n            }\n        }, [\n            editor,\n            value,\n            mounted\n        ]);\n        return null;\n    };\n    _s3(ContentSyncPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function() {\n        return {\n            focusAtEnd: function() {\n            // TODO: Implement focus at end\n            },\n            focusAtStart: function() {\n            // TODO: Implement focus at start\n            }\n        };\n    });\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"b6e8ec94f89652f1\",\n                [\n                    height ? height + \"px\" : \"100vh\"\n                ]\n            ]\n        ]) + \" \" + \"lexical-editor \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__.LexicalComposer, {\n                initialConfig: initialConfig,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"b6e8ec94f89652f1\",\n                            [\n                                height ? height + \"px\" : \"100vh\"\n                            ]\n                        ]\n                    ]) + \" \" + \"editor-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__.ContentEditable, {\n                                className: \"editor-input focus:outline-none w-full\",\n                                spellCheck: false,\n                                onKeyDown: function(e) {\n                                    console.log(\"\\uD83C\\uDFB9 键盘按下:\", {\n                                        key: e.key,\n                                        code: e.code,\n                                        ctrlKey: e.ctrlKey,\n                                        shiftKey: e.shiftKey,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                },\n                                onInput: function(e) {\n                                    console.log(\"\\uD83D\\uDCDD 输入事件:\", {\n                                        inputType: e.inputType,\n                                        data: e.data,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                },\n                                onFocus: function() {\n                                    console.log(\"\\uD83C\\uDFAF 编辑器获得焦点\");\n                                },\n                                onBlur: function() {\n                                    console.log(\"\\uD83D\\uDE34 编辑器失去焦点\");\n                                }\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__.LinkPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__.ListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 548,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__.CheckListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 549,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__.MarkdownShortcutPlugin, {\n                            transformers: customTransformers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 552,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__.HorizontalRulePlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 555,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            enabled: true,\n                            debug: \"development\" === \"development\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DebugPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 558,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListExitPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 559,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSyncPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"b6e8ec94f89652f1\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__.OnChangePlugin, {\n                            onChange: handleChange,\n                            ignoreHistoryMergeTagChange: true,\n                            ignoreSelectionChange: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 563,\n                            columnNumber: 21\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                    lineNumber: 512,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                lineNumber: 511,\n                columnNumber: 13\n            }, _this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"b6e8ec94f89652f1\",\n                dynamic: [\n                    height ? height + \"px\" : \"100vh\"\n                ],\n                children: \".lexical-editor{position:relative}.editor-container{position:relative}.editor-input{outline:none;padding:1rem 0;min-height:-webkit-calc(\".concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:-moz-calc(\").concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:calc(\").concat(height ? height + \"px\" : \"100vh\", ' - 14rem);padding-bottom:10rem;width:100%;max-width:none;line-height:1.7;font-size:1rem;color:inherit;-webkit-spellcheck:false;-moz-spellcheck:false;-ms-spellcheck:false;spellcheck:false}.lexical-editor{--lexical-indent-base-value:32px}.lexical-indent{--lexical-indent-base-value:32px}.editor-input p[style*=\"margin-left\"],.editor-input h1[style*=\"margin-left\"],.editor-input h2[style*=\"margin-left\"],.editor-input h3[style*=\"margin-left\"],.editor-input h4[style*=\"margin-left\"],.editor-input h5[style*=\"margin-left\"],.editor-input h6[style*=\"margin-left\"],.editor-input li[style*=\"margin-left\"]{-webkit-transition:margin-left.2s ease;-moz-transition:margin-left.2s ease;-o-transition:margin-left.2s ease;transition:margin-left.2s ease}.editor-placeholder{color:#999;overflow:hidden;position:absolute;-o-text-overflow:ellipsis;text-overflow:ellipsis;top:1rem;left:0;font-size:1rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;pointer-events:none}.editor-paragraph{margin:1.2rem 0;line-height:1.8}.editor-heading-h1{font-size:2.8em;font-weight:bold;margin:1.5rem 0 1rem 0;line-height:1.2}.editor-heading-h2{font-size:2.2em;font-weight:bold;margin:1.4rem 0 .8rem 0;line-height:1.3}.editor-heading-h3{font-size:1.8em;font-weight:bold;margin:1.3rem 0 .6rem 0;line-height:1.4}.editor-heading-h4{font-size:1.5em;font-weight:bold;margin:1.2rem 0 .5rem 0;line-height:1.4}.editor-heading-h5{font-size:1.3em;font-weight:bold;margin:1.1rem 0 .4rem 0;line-height:1.5}.editor-heading-h6{font-size:1.2em;font-weight:bold;margin:1rem 0 .3rem 0;line-height:1.5}.editor-quote{margin:1rem 0;padding-left:1rem;border-left:4px solid#ccc;font-style:italic;color:#666}.editor-list-ol,.editor-list-ul{margin:1rem 0;padding-left:2rem}.editor-listitem{margin:.5rem 0}.editor-link{color:#3b82f6;text-decoration:underline;cursor:pointer}.editor-link:hover{color:#1d4ed8}.editor-text-bold{font-weight:bold}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.editor-text-strikethrough{text-decoration:line-through}.editor-text-code{background-color:#e4e4e7;color:black;padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:monospace;font-size:.9em}.editor-code{background-color:#e4e4e7;color:black;border:1px solid#e9ecef;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;padding:1rem;margin:1rem 0;font-family:\"Courier New\",Courier,monospace;font-size:.9em;line-height:1.4;overflow-x:auto}[data-theme=\"dark\"] .editor-text-code{background-color:#3f3f46;color:white}[data-theme=\"dark\"] .editor-code{background-color:#3f3f46;color:white;border-color:#4b5563}.editor-list-ol{list-style-type:decimal;margin:.5rem 0;padding-left:1.5rem}.editor-list-ul{list-style-type:disc;margin:.5rem 0;padding-left:1.5rem}.editor-listitem{margin:.4rem 0;line-height:1.8}.editor-nested-listitem{margin:.3rem 0}.editor-list-ol-1{list-style-type:decimal}.editor-list-ol-2{list-style-type:lower-alpha}.editor-list-ol-3{list-style-type:lower-roman}.editor-list-ol-4{list-style-type:upper-alpha}.editor-list-ol-5{list-style-type:upper-roman}.editor-list-ul-1{list-style-type:disc}.editor-list-ul-2{list-style-type:circle}.editor-list-ul-3{list-style-type:square}.editor-list-ul-4{list-style-type:disc}.editor-list-ul-5{list-style-type:circle}.task-list{list-style:none;padding-left:0;margin:1rem 0}.task-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;margin:.5rem 0;list-style:none}.task-checkbox{margin-right:.5rem;margin-top:.125rem;cursor:pointer;width:1rem;height:1rem;border:1px solid#d1d5db;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;background:white;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-appearance:none}.task-checkbox:checked{background-color:#3b82f6;border-color:#3b82f6;color:white}.task-checkbox:checked::after{content:\"✓\";font-size:.75rem;font-weight:bold;color:white}.task-content{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:1.7}.task-item[data-checked=\"true\"] .task-content{text-decoration:line-through;opacity:.6}.lexical-highlight,mark,.editor-text-highlight{background-color:#eab834!important;color:black!important;padding:.1rem .2rem;-webkit-border-radius:.125rem;-moz-border-radius:.125rem;border-radius:.125rem}.dark .lexical-highlight,.dark mark,.dark .editor-text-highlight,[data-theme=\"dark\"] .lexical-highlight,[data-theme=\"dark\"] mark,[data-theme=\"dark\"] .editor-text-highlight,html.dark .lexical-highlight,html.dark mark,html.dark .editor-text-highlight{background-color:#3185eb!important;color:white!important}.editor-image img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}@media(prefers-color-scheme:dark){.editor-placeholder{color:#6b7280}.editor-quote{border-left-color:#4b5563;color:#9ca3af}.editor-text-code{background-color:#374151;color:#f9fafb}.editor-code{background-color:#1f2937;border-color:#374151;color:#f9fafb}}.lexical-editor ul:not([data-lexical-list-type=\"check\"]) li::marker{color:#6b7280}.lexical-editor ol li::marker{color:#6b7280}.lexical-editor .PlaygroundEditorTheme__listItemChecked,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked{position:relative;margin-left:.5em;margin-right:.5em;padding-left:1.5em;padding-right:1.5em;list-style-type:none;outline:none;display:block;min-height:1.5em}.lexical-editor .PlaygroundEditorTheme__listItemChecked>*,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked>*{margin-left:.01em}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{content:\"\";width:.9em;height:.9em;top:50%;left:0;cursor:pointer;display:block;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover;position:absolute;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.lexical-editor .PlaygroundEditorTheme__listItemChecked{text-decoration:line-through}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before{-webkit-box-shadow:0 0 0 2px#a6cdfe;-moz-box-shadow:0 0 0 2px#a6cdfe;box-shadow:0 0 0 2px#a6cdfe;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before{border:1px solid#999;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{border:1px solid rgb(61,135,245);-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;background-color:#3d87f5;background-repeat:no-repeat}.lexical-editor .PlaygroundEditorTheme__listItemChecked:after{content:\"\";cursor:pointer;border-color:#fff;border-style:solid;position:absolute;display:block;top:45%;width:.2em;left:.35em;height:.4em;-webkit-transform:translateY(-50%)rotate(45deg);-moz-transform:translateY(-50%)rotate(45deg);-ms-transform:translateY(-50%)rotate(45deg);-o-transform:translateY(-50%)rotate(45deg);transform:translateY(-50%)rotate(45deg);border-width:0 .1em .1em 0}')\n            }, void 0, false, void 0, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 510,\n        columnNumber: 9\n    }, _this);\n}, \"cp2mgoBaCDop5vv5Ezf6hVUYfiE=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n})), \"cp2mgoBaCDop5vv5Ezf6hVUYfiE=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c2 = LexicalEditor;\nLexicalEditor.displayName = \"LexicalEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Placeholder\");\n$RefreshReg$(_c1, \"LexicalEditor$forwardRef\");\n$RefreshReg$(_c2, \"LexicalEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-editor.tsx\n"));

/***/ })

});