"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/editor/lexical-main-editor.tsx":
/*!***************************************************!*\
  !*** ./components/editor/lexical-main-editor.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_without_properties_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_object_without_properties.mjs */ \"./node_modules/@swc/helpers/src/_object_without_properties.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _edit_title__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./edit-title */ \"./components/editor/edit-title.tsx\");\n/* harmony import */ var _lexical_editor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lexical-editor */ \"./components/editor/lexical-editor.tsx\");\n/* harmony import */ var _backlinks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./backlinks */ \"./components/editor/backlinks.tsx\");\n/* harmony import */ var libs_web_state_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/state/ui */ \"./libs/web/state/ui.ts\");\n/* harmony import */ var libs_shared_meta__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/shared/meta */ \"./libs/shared/meta.ts\");\n/* harmony import */ var libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/state/lexical-editor */ \"./libs/web/state/lexical-editor.ts\");\n/**\n * Lexical Main Editor Component\n * Migrated from TipTap to Lexical\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nvar LexicalMainEditor = function(_param) {\n    var className = _param.className, note = _param.note, isPreview = _param.isPreview, props = (0,_swc_helpers_src_object_without_properties_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_param, [\n        \"className\",\n        \"note\",\n        \"isPreview\"\n    ]);\n    _s();\n    var ref = libs_web_state_ui__WEBPACK_IMPORTED_MODULE_4__[\"default\"].useContainer(), settings = ref.settings.settings;\n    var ref1 = libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useContainer(), onSearchLink = ref1.onSearchLink, onCreateLink = ref1.onCreateLink, onClickLink = ref1.onClickLink, onHoverLink = ref1.onHoverLink, onEditorChange = ref1.onEditorChange, editorEl = ref1.editorEl, editorNote = ref1.note;\n    var ref2;\n    var currentEditorSize = (ref2 = note === null || note === void 0 ? void 0 : note.editorsize) !== null && ref2 !== void 0 ? ref2 : settings.editorsize;\n    var editorWidthClass;\n    switch(currentEditorSize){\n        case libs_shared_meta__WEBPACK_IMPORTED_MODULE_5__.EDITOR_SIZE.SMALL:\n            editorWidthClass = \"max-w-400\";\n            break;\n        case libs_shared_meta__WEBPACK_IMPORTED_MODULE_5__.EDITOR_SIZE.LARGE:\n            editorWidthClass = \"max-w-4xl\";\n            break;\n        case libs_shared_meta__WEBPACK_IMPORTED_MODULE_5__.EDITOR_SIZE.FULL:\n            editorWidthClass = \"max-w-full mx-4\";\n            break;\n        default:\n            editorWidthClass = \"max-w-400\";\n            break;\n    }\n    var articleClassName = className || \"pt-16 md:pt-40 px-6 m-auto h-full \".concat(editorWidthClass);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: articleClassName,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_edit_title__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                readOnly: props.readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-main-editor.tsx\",\n                lineNumber: 61,\n                columnNumber: 13\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n                ref: editorEl,\n                value: editorNote === null || editorNote === void 0 ? void 0 : editorNote.content,\n                onChange: onEditorChange,\n                onCreateLink: onCreateLink,\n                onSearchLink: onSearchLink,\n                onClickLink: onClickLink,\n                onHoverLink: onHoverLink,\n                isPreview: isPreview,\n                className: \"px-4 md:px-0\"\n            }, props), void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-main-editor.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, _this),\n            !isPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_backlinks__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-main-editor.tsx\",\n                lineNumber: 74,\n                columnNumber: 28\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\editor\\\\lexical-main-editor.tsx\",\n        lineNumber: 60,\n        columnNumber: 9\n    }, _this);\n};\n_s(LexicalMainEditor, \"l1sqHPD4RZHXuhpJbqweEzzdwUE=\", false, function() {\n    return [\n        libs_web_state_ui__WEBPACK_IMPORTED_MODULE_4__[\"default\"].useContainer,\n        libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useContainer\n    ];\n});\n_c = LexicalMainEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalMainEditor);\nvar _c;\n$RefreshReg$(_c, \"LexicalMainEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2VkaXRvci9sZXhpY2FsLW1haW4tZWRpdG9yLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBOzs7Q0FHQyxHQUVEOzs7OztBQUFxQztBQUNnQztBQUNqQztBQUNJO0FBR087QUFDZ0I7QUFFL0QsSUFBTU0saUJBQWlCLEdBTW5CLGlCQUE4QztRQUEzQ0MsU0FBUyxVQUFUQSxTQUFTLEVBQUVDLElBQUksVUFBSkEsSUFBSSxFQUFFQyxTQUFTLFVBQVRBLFNBQVMsRUFBS0MsS0FBSztRQUFwQ0gsV0FBUztRQUFFQyxNQUFJO1FBQUVDLFdBQVM7OztJQUM3QixJQUVJTixHQUFzQixHQUF0QkEsc0VBQW9CLEVBQUUsRUFEdEJTLFFBQW9CLEdBQ3BCVCxHQUFzQixDQUR0QlMsUUFBUSxDQUFJQSxRQUFRO0lBR3hCLElBUUlQLElBQWlDLEdBQWpDQSxrRkFBK0IsRUFBRSxFQVBqQ1EsWUFBWSxHQU9aUixJQUFpQyxDQVBqQ1EsWUFBWSxFQUNaQyxZQUFZLEdBTVpULElBQWlDLENBTmpDUyxZQUFZLEVBQ1pDLFdBQVcsR0FLWFYsSUFBaUMsQ0FMakNVLFdBQVcsRUFDWEMsV0FBVyxHQUlYWCxJQUFpQyxDQUpqQ1csV0FBVyxFQUNYQyxjQUFjLEdBR2RaLElBQWlDLENBSGpDWSxjQUFjLEVBQ2RDLFFBQVEsR0FFUmIsSUFBaUMsQ0FGakNhLFFBQVEsRUFDUlYsVUFBZ0IsR0FDaEJILElBQWlDLENBRGpDRyxJQUFJO1FBR2tCQSxJQUFnQjtJQUExQyxJQUFNWSxpQkFBaUIsR0FBR1osQ0FBQUEsSUFBZ0IsR0FBaEJBLElBQUksYUFBSkEsSUFBSSxXQUFZLEdBQWhCQSxLQUFBQSxDQUFnQixHQUFoQkEsSUFBSSxDQUFFYSxVQUFVLGNBQWhCYixJQUFnQixjQUFoQkEsSUFBZ0IsR0FBSUksUUFBUSxDQUFDUyxVQUFVO0lBRWpFLElBQUlDLGdCQUFnQjtJQUNwQixPQUFRRixpQkFBaUI7UUFDckIsS0FBS2hCLCtEQUFpQjtZQUNsQmtCLGdCQUFnQixHQUFHLFdBQVcsQ0FBQztZQUMvQixNQUFNO1FBQ1YsS0FBS2xCLCtEQUFpQjtZQUNsQmtCLGdCQUFnQixHQUFHLFdBQVcsQ0FBQztZQUMvQixNQUFNO1FBQ1YsS0FBS2xCLDhEQUFnQjtZQUNqQmtCLGdCQUFnQixHQUFHLGlCQUFpQixDQUFDO1lBQ3JDLE1BQU07UUFDVjtZQUNJQSxnQkFBZ0IsR0FBRyxXQUFXLENBQUM7WUFDL0IsTUFBTTtLQUNiO0lBRUQsSUFBTUksZ0JBQWdCLEdBQ2xCbkIsU0FBUyxJQUFJLG9DQUFtQyxDQUFtQixPQUFqQmUsZ0JBQWdCLENBQUU7SUFJeEUscUJBQ0ksOERBQUNLLFNBQU87UUFBQ3BCLFNBQVMsRUFBRW1CLGdCQUFnQjs7MEJBQ2hDLDhEQUFDMUIsbURBQVM7Z0JBQUM0QixRQUFRLEVBQUVsQixLQUFLLENBQUNrQixRQUFROzs7OztxQkFBSTswQkFDdkMsOERBQUMzQix1REFBYTtnQkFDVjRCLEdBQUcsRUFBRVgsUUFBUTtnQkFDYlksS0FBSyxFQUFFWCxVQUFVLGFBQVZBLFVBQVUsV0FBUyxHQUFuQkEsS0FBQUEsQ0FBbUIsR0FBbkJBLFVBQVUsQ0FBRVksT0FBTztnQkFDMUJDLFFBQVEsRUFBRWYsY0FBYztnQkFDeEJILFlBQVksRUFBRUEsWUFBWTtnQkFDMUJELFlBQVksRUFBRUEsWUFBWTtnQkFDMUJFLFdBQVcsRUFBRUEsV0FBVztnQkFDeEJDLFdBQVcsRUFBRUEsV0FBVztnQkFDeEJQLFNBQVMsRUFBRUEsU0FBUztnQkFDcEJGLFNBQVMsRUFBQyxjQUFjO2VBQ3BCRyxLQUFLOzs7O3FCQUNYO1lBQ0QsQ0FBQ0QsU0FBUyxrQkFBSSw4REFBQ1Asa0RBQVM7Ozs7cUJBQUc7Ozs7OzthQUN0QixDQUNaO0FBQ04sQ0FBQztHQTlES0ksaUJBQWlCOztRQVNmSCxzRUFBb0I7UUFVcEJFLGtGQUErQjs7O0FBbkJqQ0MsS0FBQUEsaUJBQWlCO0FBZ0V2QiwrREFBZUEsaUJBQWlCLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9lZGl0b3IvbGV4aWNhbC1tYWluLWVkaXRvci50c3g/NzJjOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIExleGljYWwgTWFpbiBFZGl0b3IgQ29tcG9uZW50XG4gKiBNaWdyYXRlZCBmcm9tIFRpcFRhcCB0byBMZXhpY2FsXG4gKi9cblxuaW1wb3J0IEVkaXRUaXRsZSBmcm9tICcuL2VkaXQtdGl0bGUnO1xuaW1wb3J0IExleGljYWxFZGl0b3IsIHsgTGV4aWNhbEVkaXRvclByb3BzIH0gZnJvbSAnLi9sZXhpY2FsLWVkaXRvcic7XG5pbXBvcnQgQmFja2xpbmtzIGZyb20gJy4vYmFja2xpbmtzJztcbmltcG9ydCBVSVN0YXRlIGZyb20gJ2xpYnMvd2ViL3N0YXRlL3VpJztcbmltcG9ydCB7IEZDIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgTm90ZU1vZGVsIH0gZnJvbSAnbGlicy9zaGFyZWQvbm90ZSc7XG5pbXBvcnQgeyBFRElUT1JfU0laRSB9IGZyb20gJ2xpYnMvc2hhcmVkL21ldGEnO1xuaW1wb3J0IExleGljYWxFZGl0b3JTdGF0ZSBmcm9tICdsaWJzL3dlYi9zdGF0ZS9sZXhpY2FsLWVkaXRvcic7XG5cbmNvbnN0IExleGljYWxNYWluRWRpdG9yOiBGQzxcbiAgICBMZXhpY2FsRWRpdG9yUHJvcHMgJiB7XG4gICAgICAgIG5vdGU/OiBOb3RlTW9kZWw7XG4gICAgICAgIGlzUHJldmlldz86IGJvb2xlYW47XG4gICAgICAgIGNsYXNzTmFtZT86IHN0cmluZztcbiAgICB9XG4+ID0gKHsgY2xhc3NOYW1lLCBub3RlLCBpc1ByZXZpZXcsIC4uLnByb3BzIH0pID0+IHtcbiAgICBjb25zdCB7XG4gICAgICAgIHNldHRpbmdzOiB7IHNldHRpbmdzIH0sXG4gICAgfSA9IFVJU3RhdGUudXNlQ29udGFpbmVyKCk7XG5cbiAgICBjb25zdCB7XG4gICAgICAgIG9uU2VhcmNoTGluayxcbiAgICAgICAgb25DcmVhdGVMaW5rLFxuICAgICAgICBvbkNsaWNrTGluayxcbiAgICAgICAgb25Ib3ZlckxpbmssXG4gICAgICAgIG9uRWRpdG9yQ2hhbmdlLFxuICAgICAgICBlZGl0b3JFbCxcbiAgICAgICAgbm90ZTogZWRpdG9yTm90ZSxcbiAgICB9ID0gTGV4aWNhbEVkaXRvclN0YXRlLnVzZUNvbnRhaW5lcigpO1xuXG4gICAgY29uc3QgY3VycmVudEVkaXRvclNpemUgPSBub3RlPy5lZGl0b3JzaXplID8/IHNldHRpbmdzLmVkaXRvcnNpemU7XG5cbiAgICBsZXQgZWRpdG9yV2lkdGhDbGFzczogc3RyaW5nO1xuICAgIHN3aXRjaCAoY3VycmVudEVkaXRvclNpemUpIHtcbiAgICAgICAgY2FzZSBFRElUT1JfU0laRS5TTUFMTDpcbiAgICAgICAgICAgIGVkaXRvcldpZHRoQ2xhc3MgPSAnbWF4LXctNDAwJztcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIEVESVRPUl9TSVpFLkxBUkdFOlxuICAgICAgICAgICAgZWRpdG9yV2lkdGhDbGFzcyA9ICdtYXgtdy00eGwnO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgRURJVE9SX1NJWkUuRlVMTDpcbiAgICAgICAgICAgIGVkaXRvcldpZHRoQ2xhc3MgPSAnbWF4LXctZnVsbCBteC00JztcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgZWRpdG9yV2lkdGhDbGFzcyA9ICdtYXgtdy00MDAnO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgfVxuXG4gICAgY29uc3QgYXJ0aWNsZUNsYXNzTmFtZSA9XG4gICAgICAgIGNsYXNzTmFtZSB8fCBgcHQtMTYgbWQ6cHQtNDAgcHgtNiBtLWF1dG8gaC1mdWxsICR7ZWRpdG9yV2lkdGhDbGFzc31gO1xuXG5cblxuICAgIHJldHVybiAoXG4gICAgICAgIDxhcnRpY2xlIGNsYXNzTmFtZT17YXJ0aWNsZUNsYXNzTmFtZX0+XG4gICAgICAgICAgICA8RWRpdFRpdGxlIHJlYWRPbmx5PXtwcm9wcy5yZWFkT25seX0gLz5cbiAgICAgICAgICAgIDxMZXhpY2FsRWRpdG9yXG4gICAgICAgICAgICAgICAgcmVmPXtlZGl0b3JFbH1cbiAgICAgICAgICAgICAgICB2YWx1ZT17ZWRpdG9yTm90ZT8uY29udGVudH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17b25FZGl0b3JDaGFuZ2V9XG4gICAgICAgICAgICAgICAgb25DcmVhdGVMaW5rPXtvbkNyZWF0ZUxpbmt9XG4gICAgICAgICAgICAgICAgb25TZWFyY2hMaW5rPXtvblNlYXJjaExpbmt9XG4gICAgICAgICAgICAgICAgb25DbGlja0xpbms9e29uQ2xpY2tMaW5rfVxuICAgICAgICAgICAgICAgIG9uSG92ZXJMaW5rPXtvbkhvdmVyTGlua31cbiAgICAgICAgICAgICAgICBpc1ByZXZpZXc9e2lzUHJldmlld31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IG1kOnB4LTBcIlxuICAgICAgICAgICAgICAgIHsuLi5wcm9wc31cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICB7IWlzUHJldmlldyAmJiA8QmFja2xpbmtzIC8+fVxuICAgICAgICA8L2FydGljbGU+XG4gICAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IExleGljYWxNYWluRWRpdG9yO1xuIl0sIm5hbWVzIjpbIkVkaXRUaXRsZSIsIkxleGljYWxFZGl0b3IiLCJCYWNrbGlua3MiLCJVSVN0YXRlIiwiRURJVE9SX1NJWkUiLCJMZXhpY2FsRWRpdG9yU3RhdGUiLCJMZXhpY2FsTWFpbkVkaXRvciIsImNsYXNzTmFtZSIsIm5vdGUiLCJpc1ByZXZpZXciLCJwcm9wcyIsInVzZUNvbnRhaW5lciIsInNldHRpbmdzIiwib25TZWFyY2hMaW5rIiwib25DcmVhdGVMaW5rIiwib25DbGlja0xpbmsiLCJvbkhvdmVyTGluayIsIm9uRWRpdG9yQ2hhbmdlIiwiZWRpdG9yRWwiLCJlZGl0b3JOb3RlIiwiY3VycmVudEVkaXRvclNpemUiLCJlZGl0b3JzaXplIiwiZWRpdG9yV2lkdGhDbGFzcyIsIlNNQUxMIiwiTEFSR0UiLCJGVUxMIiwiYXJ0aWNsZUNsYXNzTmFtZSIsImFydGljbGUiLCJyZWFkT25seSIsInJlZiIsInZhbHVlIiwiY29udGVudCIsIm9uQ2hhbmdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/editor/lexical-main-editor.tsx\n"));

/***/ })

});